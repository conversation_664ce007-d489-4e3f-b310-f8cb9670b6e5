import { useState, useCallback } from 'react';
import { useDashboardContext } from '../context/use-dashboard-context';
import { Demo } from '../types/demo';
import { load as _load, add as _add, update as _update, remove as _remove } from '../services/customerService';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../app/state/store';
import { generateUuid } from '../assets/functions';

export const useCustomers = (toast: React.MutableRefObject<any>) => {
    const dispatch = useDispatch<AppDispatch>();

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const load = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            return await dispatch(_load()).unwrap();
        } catch (err) {
            console.error('Failed to load customers:', err);
            setError('Failed to load customers');
            toast.current.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load customers',
                life: 3000
            });
        } finally {
            setLoading(false);
        }
    }, [dispatch, toast]);

    const add = useCallback(
        async (newCustomer: Demo.Customer, setCustomers?: React.Dispatch<React.SetStateAction<Demo.Customer[]>>, setUuidToCustomer?: React.Dispatch<React.SetStateAction<Record<string, Demo.Customer>>>) => {
            setLoading(true);
            setError(null);

            try {
                const _coordinator = newCustomer.coordinator as Demo.Customer;
                const _insertDate = newCustomer.insertDate instanceof Date ? newCustomer.insertDate.toISOString() : newCustomer.insertDate;

                const _customerForRedux = {
                    ...newCustomer,
                    uuid: generateUuid(),
                    coordinator: _coordinator.uuid,
                    insertDate: _insertDate
                };

                const _customer = await dispatch(_add(_customerForRedux)).unwrap();

                if (setCustomers && setUuidToCustomer) {
                    const _mappedCustomer = {
                        ..._customer,
                        coordinator: _coordinator
                    };

                    setCustomers((prev) => [...prev, _mappedCustomer]);
                    setUuidToCustomer((prev) => ({ ...prev, [_customer.uuid]: _mappedCustomer }));
                }

                toast.current.show({
                    severity: 'success',
                    summary: 'Successful',
                    detail: 'Customer Created',
                    life: 2000
                });
            } catch (err) {
                console.error('Failed to add customer:', err);
                setError('Failed to add customer');
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to add customer',
                    life: 3000
                });
            } finally {
                setLoading(false);
            }
        },
        [dispatch, toast]
    );

    const update = useCallback(
        async (customer: Demo.Customer, setCustomers?: React.Dispatch<React.SetStateAction<Demo.Customer[]>>, setUuidToCustomer?: React.Dispatch<React.SetStateAction<Record<string, Demo.Customer>>>) => {
            setLoading(true);
            setError(null);

            try {
                const _element = await dispatch(_update(customer)).unwrap();
                setCustomers((prev) => prev.map((c) => (c.uuid === _element.uuid ? customer : c)));
                setUuidToCustomer((prev) => ({ ...prev, [customer.uuid]: customer }));
                toast.current.show({
                    severity: 'success',
                    summary: 'Successful',
                    detail: 'Customer Updated',
                    life: 2000
                });
            } catch (err) {
                console.error('Failed to update customer:', err);
                setError('Failed to update customer');
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to update customer',
                    life: 3000
                });
            } finally {
                setLoading(false);
            }
        },
        [dispatch, toast]
    );

    const remove = useCallback(
        async (customerId: string, setCustomers?: React.Dispatch<React.SetStateAction<Demo.Customer[]>>, setUuidToCustomer?: React.Dispatch<React.SetStateAction<Record<string, Demo.Customer>>>) => {
            setLoading(true);
            setError(null);

            try {
                await dispatch(_remove(customerId)).unwrap();
                setCustomers((prev) => prev.filter((customer) => customer.uuid !== customerId));
                setUuidToCustomer((prev) => {
                    const newMap = { ...prev };
                    delete newMap[customerId];
                    return newMap;
                });
            } catch (err) {
                console.error('Failed to delete customer:', err);
                setError('Failed to delete customer');
            } finally {
                setLoading(false);
            }
        },
        [dispatch, toast]
    );

    return {
        loadCustomers: load,
        addCustomer: add,
        updateCustomer: update,
        deleteCustomer: remove,
        loading,
        error
    };
};
