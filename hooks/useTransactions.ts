import { useCallback, useState } from 'react';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../app/state/store';
import { add as _add, load as _load, remove as _remove, update as _update } from '../services/transactionService';
import { Demo } from '../types/demo';
import { generateUuid } from '../assets/functions';

export const useTransactions = (toast: React.MutableRefObject<any>) => {
    const dispatch = useDispatch<AppDispatch>();

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const load = useCallback(
        async (customers: Demo.Customer[], uuidToCustomer: Record<string, Demo.Customer>) => {
            setLoading(true);
            setError(null);

            try {
                const _transactions = await dispatch(_load(customers)).unwrap();

                const _mappedTransactions = _transactions.map((_transaction) => ({
                    ..._transaction,
                    customer: uuidToCustomer[_transaction.customer as string],
                    coordinator: uuidToCustomer[_transaction.coordinator as string]
                }));

                return _mappedTransactions;
            } catch (err) {
                console.error('Failed to load transactions:', err);
                setError('Failed to load transactions');
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to load transactions',
                    life: 3000
                });
            } finally {
                setLoading(false);
            }
        },
        [dispatch, toast]
    );

    const add = useCallback(
        async (newTransaction: Demo.Transaction, setTransactions?: React.Dispatch<React.SetStateAction<Demo.Transaction[]>>) => {
            setLoading(true);
            setError(null);

            try {
                const _customer = newTransaction.customer as Demo.Customer;
                const _coordinator = newTransaction.coordinator as Demo.Customer;
                const _insertDate = newTransaction.insertDate instanceof Date ? newTransaction.insertDate.toISOString() : newTransaction.insertDate;

                const _transactionForRedux = {
                    ...newTransaction,
                    uuid: generateUuid(),
                    name: `${_customer.firstname} ${_customer.lastname}`,
                    customer: _customer.uuid,
                    coordinator: _coordinator.uuid,
                    insertDate: _insertDate
                };

                const _transaction = await dispatch(_add(_transactionForRedux)).unwrap();

                // Add to local state with proper object references
                if (setTransactions) {
                    const _mappedTransaction = {
                        ..._transaction,
                        customer: _customer,
                        coordinator: _coordinator
                    };

                    setTransactions((prev) => [...prev, _mappedTransaction]);
                }
                toast.current.show({
                    severity: 'success',
                    summary: 'Successful',
                    detail: 'Transaction Added',
                    life: 2000
                });
                return _transaction;
            } catch (err) {
                console.error('Failed to add transaction:', err);
                setError('Failed to add transaction');
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to add transaction',
                    life: 3000
                });
            } finally {
                setLoading(false);
            }
        },
        [dispatch, toast]
    );

    const update = useCallback(
        async (transaction: Demo.Transaction, setTransactions?: React.Dispatch<React.SetStateAction<Demo.Transaction[]>>) => {
            setLoading(true);
            setError(null);

            try {
                const _customer = transaction.customer as Demo.Customer;
                const _coordinator = transaction.coordinator as Demo.Customer;
                const _insertDate = transaction.insertDate instanceof Date ? transaction.insertDate.toISOString() : transaction.insertDate;

                const _transactionForRedux = {
                    ...transaction,
                    customer: _customer.uuid,
                    coordinator: _coordinator.uuid,
                    name: `${_customer.firstname} ${_customer.lastname}`,
                    insertDate: _insertDate
                };

                const _updatedTransaction = await dispatch(_update(_transactionForRedux)).unwrap();

                const _mappedTransaction = {
                    ..._updatedTransaction,
                    customer: _customer,
                    coordinator: _coordinator
                };

                setTransactions((prev) => prev.map((_t) => (_t.uuid === _mappedTransaction.uuid ? _mappedTransaction : _t)));
                toast.current.show({
                    severity: 'success',
                    summary: 'Successful',
                    detail: 'Transaction Updated',
                    life: 2000
                });
            } catch (err) {
                console.error('Failed to update transaction:', err);
                setError('Failed to update transaction');
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to update transaction',
                    life: 3000
                });
            } finally {
                setLoading(false);
            }
        },
        [dispatch, toast]
    );

    const remove = useCallback(
        async (transactionId: string, setTransactions?: React.Dispatch<React.SetStateAction<Demo.Transaction[]>>) => {
            setLoading(true);
            setError(null);

            try {
                await dispatch(_remove(transactionId)).unwrap();
                setTransactions((prev) => prev.filter((_t) => _t.uuid !== transactionId));
                toast.current.show({
                    severity: 'success',
                    summary: 'Successful',
                    detail: 'Transaction Removed',
                    life: 2000
                });
            } catch (err) {
                console.error('Failed to remove transaction:', err);
                setError('Failed to remove transaction');
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to remove transaction',
                    life: 3000
                });
            } finally {
                setLoading(false);
            }
        },
        [dispatch, toast]
    );

    return {
        loadTransactions: load,
        addTransaction: add,
        updateTransaction: update,
        deleteTransaction: remove,
        loading,
        error
    };
};
