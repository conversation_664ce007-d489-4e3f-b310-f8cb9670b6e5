import { useSession } from 'next-auth/react';
import axios from '../app/api/axios';

export const useRefreshToken = () => {
    const { data: session } = useSession();

    const refreshToken = async () => {
        const response = await axios.get('/auth/refresh');
        if (session && response.data?.accessToken) session.accessToken = response.data?.accessToken;
    };

    return refreshToken;
};
