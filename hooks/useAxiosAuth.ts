import { useSession } from "next-auth/react";
import { useEffect } from "react";
import { axiosSecure } from "../app/api/axios";
import { useRefreshToken } from "./useRefreshToken";


const useAxiosAuth = () => {
    const { data: session } = useSession();
    const refresh = useRefreshToken();

    useEffect(() => {
        const requestInterceptor = axiosSecure.interceptors.request.use((config) => {
            if(!config.headers.Authorization && session?.accessToken){
                config.headers.Authorization = `Bearer ${session?.accessToken}`;
            }
            return config;
        }, (error) => Promise.reject(error)
    );

        const responseInterceptor = axiosSecure.interceptors.response.use(
            (response) => response,
            async (error) => {
                const prevRequest = error.config;
                if(error.response.status === 401 && prevRequest && !prevRequest._retry){
                    prevRequest._retry = true;
                    await refresh();
                    prevRequest.headers.Authorization = `Bearer ${session.accessToken}`;
                    return axiosSecure(prevRequest);
                }
                return Promise.reject(error);
            }
        );

        return () => {
            axiosSecure.interceptors.request.eject(requestInterceptor);
            axiosSecure.interceptors.response.eject(responseInterceptor);
        };
    }, [session]);

    return axiosSecure;
};

export default useAxiosAuth;