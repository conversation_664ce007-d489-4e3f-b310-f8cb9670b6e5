import { useState, useCallback } from 'react';
import { useDashboardContext } from '../context/use-dashboard-context';
import { Demo } from '../types/demo';
import { load as _load, add as _add, update as _update, remove as _remove } from '../services/paymentService';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../app/state/store';
import { generateUuid } from '../assets/functions';

export const usePayments = (toast: React.MutableRefObject<any>) => {
    const dispatch = useDispatch<AppDispatch>();

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const load = useCallback(
        async (transactions: Demo.Transaction[], uuidToTransaction: Record<string, Demo.Transaction>) => {
            setLoading(true);
            setError(null);

            try {
                const _payments = await dispatch(_load(transactions)).unwrap();
                const _mappedPayments = _payments.map((_payment) => ({
                    ..._payment,
                    transaction: uuidToTransaction[_payment.transaction as string]
                }));
                return _mappedPayments;
            } catch (err) {
                console.error('Failed to load payments:', err);
                setError('Failed to load payments');
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to load payments',
                    life: 3000
                });
            } finally {
                setLoading(false);
            }
        },
        [dispatch, toast]
    );

    const add = useCallback(
        async (newPayment: Demo.Payment, setPayments?: React.Dispatch<React.SetStateAction<Demo.Payment[]>>) => {
            setLoading(true);
            setError(null);

            try {
                const _transaction = newPayment.transaction as Demo.Transaction;
                const _insertDate = newPayment.insertDate instanceof Date ? newPayment.insertDate.toISOString() : newPayment.insertDate;

                const _paymentForRedux = {
                    ...newPayment,
                    uuid: generateUuid(),
                    name: _transaction?.name,
                    transaction: _transaction.uuid,
                    insertDate: _insertDate
                };

                const _payment = await dispatch(_add(_paymentForRedux)).unwrap();

                // Update local state with proper object references if setPayments is provided
                if (setPayments) {
                    const mappedPayment = {
                        ..._payment,
                        transaction: _transaction || _payment.transaction
                    };

                    setPayments((prev) => [...prev, mappedPayment]);
                }

                toast.current.show({
                    severity: 'success',
                    summary: 'Successful',
                    detail: 'Payment Added',
                    life: 2000
                });

                return _payment;
            } catch (err) {
                console.error('Failed to add payment:', err);
                setError('Failed to add payment');
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to add payment',
                    life: 3000
                });
            } finally {
                setLoading(false);
            }
        },
        [dispatch, toast]
    );

    const update = useCallback(
        async (payment: Demo.Payment, setPayments?: React.Dispatch<React.SetStateAction<Demo.Payment[]>>) => {
            setLoading(true);
            setError(null);

            try {
                const updatedPayment = await dispatch(_update(payment)).unwrap();
                setPayments((prev) => prev.map((p) => (p.uuid === updatedPayment.uuid ? updatedPayment : p)));
                toast.current.show({
                    severity: 'success',
                    summary: 'Successful',
                    detail: 'Payment Updated',
                    life: 2000
                });
            } catch (err) {
                console.error('Failed to update payment:', err);
                setError('Failed to update payment');
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to update payment',
                    life: 3000
                });
            } finally {
                setLoading(false);
            }
        },
        [dispatch, toast]
    );

    const remove = useCallback(
        async (paymentId: string, setPayments?: React.Dispatch<React.SetStateAction<Demo.Payment[]>>) => {
            setLoading(true);
            setError(null);

            try {
                await dispatch(_remove(paymentId)).unwrap();
                setPayments((prev) => prev.filter((p) => p.uuid !== paymentId));
                toast.current.show({
                    severity: 'success',
                    summary: 'Successful',
                    detail: 'Payment Removed',
                    life: 2000
                });
            } catch (err) {
                console.error('Failed to remove payment:', err);
                setError('Failed to remove payment');
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to remove payment',
                    life: 3000
                });
            } finally {
                setLoading(false);
            }
        },
        [dispatch, toast]
    );

    return {
        loadPayments: load,
        addPayment: add,
        updatePayment: update,
        deletePayment: remove,
        loading,
        error
    };
};
