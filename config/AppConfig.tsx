'use client';

import { PrimeReactContext } from 'primereact/api';
import { InputSwitch, InputSwitchChangeEvent } from 'primereact/inputswitch';
import { Sidebar } from 'primereact/sidebar';
import { useContext, useEffect } from 'react';
import AppColorSchemeConfig from './styles/ColorSchemeConfig';
import ComponentThemesConfig from './styles/ComponentThemesConfig';
import MenuThemesConfig from './styles/MenuThemesConfig';
import MenuTypeConfig from './styles/MenuTypeConfig';
import ScaleConfig from './styles/ScaleConfig';
import { AppConfigProps } from '../types/layout';
import { LayoutContext } from '../context/LayoutProvider';
import InputStyleConfig from './styles/InputStyleConfig';

const AppConfig = (props: AppConfigProps) => {
    const { layoutConfig, setLayoutConfig, layoutState, setLayoutState, isSlim, isHorizontal, isCompact } = useContext(LayoutContext);
    const { setRipple, changeTheme } = useContext(PrimeReactContext);

    useEffect(() => {
        if (isSlim() || isHorizontal() || isCompact()) {
            setLayoutState((prevState) => ({ ...prevState, resetMenu: true }));
        }
    }, [layoutConfig.menuMode]);

    const changeRipple = (e: InputSwitchChangeEvent) => {
        setRipple(e.value);
        setLayoutConfig((prevState) => ({ ...prevState, ripple: e.value }));
    };

    const applyScale = () => {
        document.documentElement.style.fontSize = layoutConfig.scale + 'px';
    };

    useEffect(() => {
        applyScale();
    }, [layoutConfig.scale]);

    return (
        <div id="layout-config">
            <a
                className="layout-config-button"
                onClick={() =>
                    setLayoutState((prevState) => ({
                        ...prevState,
                        configSidebarVisible: true
                    }))
                }
            >
                <i className="pi pi-cog"></i>
            </a>

            <Sidebar
                visible={layoutState.configSidebarVisible}
                position="right"
                onHide={() =>
                    setLayoutState((prevState) => ({
                        ...prevState,
                        configSidebarVisible: false
                    }))
                }
            >
                <div className={`w-full sm:w-18rem`} style={{ transition: '.3s cubic-bezier(0, 0, 0.2, 1)' }}>
                    <AppColorSchemeConfig changeTheme={changeTheme} />
                    <hr />

                    {!props.minimal && (
                        <>
                            <MenuTypeConfig />
                            <hr />
                            <MenuThemesConfig />
                            <hr />
                        </>
                    )}

                    <ComponentThemesConfig />
                    <hr />

                    <ScaleConfig />
                    <hr />

                    {!props.minimal && <InputStyleConfig />}
                    <hr />

                    <h5>Ripple Effect</h5>
                    <InputSwitch checked={layoutConfig.ripple} onChange={(e) => changeRipple(e)} />
                </div>
            </Sidebar>
        </div>
    );
};

export default AppConfig;
