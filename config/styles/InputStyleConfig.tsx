import { RadioButton, RadioButtonChangeEvent } from 'primereact/radiobutton';
import { useContext } from 'react';
import { LayoutContext } from '../../context/LayoutProvider';

const InputStyleConfig = () => {
    const { layoutConfig, setLayoutConfig } = useContext(LayoutContext);

    const changeInputStyle = (e: RadioButtonChangeEvent) => {
        setLayoutConfig((prevState) => ({ ...prevState, inputStyle: e.value }));
    };

    return (
        <>
            <h5>Input Style</h5>
            <div className="flex">
                <div className="field-radiobutton flex-1">
                    <RadioButton inputId="input_outlined" name="inputstyle" value="outlined" checked={layoutConfig.inputStyle === 'outlined'} onChange={(e) => changeInputStyle(e)} />
                    <label htmlFor="input_outlined">Outlined</label>
                </div>
                <div className="field-radiobutton flex-1">
                    <RadioButton inputId="input_filled" name="inputstyle" value="filled" checked={layoutConfig.inputStyle === 'filled'} onChange={(e) => changeInputStyle(e)} />
                    <label htmlFor="input_filled">Filled</label>
                </div>
            </div>
        </>
    );
};

export default InputStyleConfig;
