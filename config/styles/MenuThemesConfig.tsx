'use client';

import { useContext } from 'react';
import { LayoutContext } from '../../context/LayoutProvider';
import { MenuTheme } from '../../types/layout';
import { menuThemes } from '../../assets/constants';

const MenuThemesConfig = () => {
    const { layoutConfig, setLayoutConfig } = useContext(LayoutContext);

    const changeMenuTheme = (theme: MenuTheme) => {
        setLayoutConfig((prevState) => ({ ...prevState, menuTheme: theme.name }));
    };

    const getMenuThemes = () => {
        if (layoutConfig.colorScheme === 'light') {
            return (
                <div className="flex flex-wrap gap-3">
                    {menuThemes
                        .filter((theme) => theme.visible)
                        .map((theme) => {
                            const checkStyle = theme.name === 'white' ? 'black' : 'white';
                            return (
                                <div key={theme.name}>
                                    <a
                                        className="w-2rem shadow-2 h-2rem cursor-pointer hover:shadow-4 border-round transition-duration-150 flex align-items-center justify-content-center"
                                        style={{ cursor: 'pointer', backgroundColor: theme.color }}
                                        onClick={() => changeMenuTheme(theme)}
                                    >
                                        {layoutConfig.menuTheme === theme.name && (
                                            <span className="check flex align-items-center justify-content-center">
                                                <i className="pi pi-check" style={{ color: checkStyle }}></i>
                                            </span>
                                        )}
                                    </a>
                                </div>
                            );
                        })}
                </div>
            );
        }

        return (
            <div>
                <p>Menu themes are only available in light mode and static, slim, overlay menu modes by design as large surfaces can emit too much brightness in dark mode.</p>
            </div>
        );
    };

    const menuThemesElement = getMenuThemes();

    return (
        <>
            <h5>Menu Themes</h5>
            {menuThemesElement}
        </>
    );
};

export default MenuThemesConfig;
