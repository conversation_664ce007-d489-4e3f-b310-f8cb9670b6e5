'use client';

import { useContext } from 'react';
import { LayoutContext } from '../../context/LayoutProvider';
import { componentThemes } from '../../assets/constants';
import { PrimeReactContext } from 'primereact/api';

const ComponentThemesConfig = () => {
    const { layoutConfig, setLayoutConfig } = useContext(LayoutContext);
    const { changeTheme } = useContext(PrimeReactContext);

    const _changeTheme = (theme: string) => {
        changeTheme(layoutConfig.theme, theme, 'theme-link', () => {
            setLayoutConfig((prevState) => ({ ...prevState, theme }));
        });
    };

    const getComponentThemes = () => {
        return (
            <div className="layout-themes flex flex-wrap gap-3">
                {componentThemes.map((theme, i) => {
                    return (
                        <div key={i}>
                            <a
                                className="w-2rem h-2rem shadow-2 cursor-pointer hover:shadow-4 border-round transition-duration-150 flex align-items-center justify-content-center"
                                style={{ cursor: 'pointer', backgroundColor: theme.color }}
                                onClick={() => _changeTheme(theme.name)}
                            >
                                {layoutConfig.theme === theme.name && (
                                    <span className="check flex align-items-center justify-content-center">
                                        <i className="pi pi-check" style={{ color: 'white' }}></i>
                                    </span>
                                )}
                            </a>
                        </div>
                    );
                })}
            </div>
        );
    };

    const componentThemesElement = getComponentThemes();

    return (
        <>
            <h5>Component Themes</h5>
            {componentThemesElement}
        </>
    );
};

export default ComponentThemesConfig;
