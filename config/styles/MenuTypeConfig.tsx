import { RadioButton, RadioButtonChangeEvent } from 'primereact/radiobutton';
import React, { useContext } from 'react';
import { LayoutContext } from '../../context/LayoutProvider';

const MenuTypeConfig = () => {
    const { layoutConfig, setLayoutConfig } = useContext(LayoutContext);

    const changeMenuMode = (e: RadioButtonChangeEvent) => {
        setLayoutConfig((prevState) => ({ ...prevState, menuMode: e.value }));
    };

    return (
        <>
            <h5>Menu Type</h5>
            <div className="flex flex-wrap row-gap-3">
                <div className="flex align-items-center gap-2 w-6">
                    <RadioButton name="menuMode" value="static" checked={layoutConfig.menuMode === 'static'} inputId="mode1" onChange={(e) => changeMenuMode(e)}></RadioButton>
                    <label htmlFor="mode1">Static</label>
                </div>
                <div className="flex align-items-center gap-2 w-6">
                    <RadioButton name="menuMode" value="slim" checked={layoutConfig.menuMode === 'slim'} inputId="mode3" onChange={(e) => changeMenuMode(e)}></RadioButton>
                    <label htmlFor="mode3">Slim</label>
                </div>
                <div className="flex align-items-center gap-2 w-6">
                    <RadioButton name="menuMode" value="horizontal" checked={layoutConfig.menuMode === 'horizontal'} inputId="mode4" onChange={(e) => changeMenuMode(e)}></RadioButton>
                    <label htmlFor="mode4">Horizontal</label>
                </div>
                <div className="flex align-items-center gap-2 w-6">
                    <RadioButton name="menuMode" value="drawer" checked={layoutConfig.menuMode === 'drawer'} inputId="mode6" onChange={(e) => changeMenuMode(e)}></RadioButton>
                    <label htmlFor="mode6">Drawer</label>
                </div>
            </div>
        </>
    );
};

export default MenuTypeConfig;
