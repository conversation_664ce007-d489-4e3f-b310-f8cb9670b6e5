'use client';

import { RadioButton } from 'primereact/radiobutton';
import { useContext } from 'react';
import { ColorScheme } from '../../types/layout';
import { LayoutContext } from '../../context/LayoutProvider';

const AppColorSchemeConfig = ({ changeTheme }) => {
    const { layoutConfig, setLayoutConfig } = useContext(LayoutContext);

    const changeColorScheme = (colorScheme: ColorScheme) => {
        changeTheme(layoutConfig.colorScheme, colorScheme, 'theme-link', () => {
            setLayoutConfig((prevState) => ({ ...prevState, colorScheme }));
        });
    };

    return (
        <>
            <h5>Color Scheme</h5>
            <div className="flex">
                <div className="field-radiobutton flex-auto">
                    <RadioButton name="colorScheme" value="light" checked={layoutConfig.colorScheme === 'light'} inputId="theme3" onChange={(e) => changeColorScheme(e.value)}></RadioButton>
                    <label htmlFor="theme3">Light</label>
                </div>
                <div className="field-radiobutton flex-auto">
                    <RadioButton name="colorScheme" value="dim" checked={layoutConfig.colorScheme === 'dim'} inputId="theme2" onChange={(e) => changeColorScheme(e.value)}></RadioButton>
                    <label htmlFor="theme2">Dim</label>
                </div>
                <div className="field-radiobutton flex-auto">
                    <RadioButton name="colorScheme" value="dark" checked={layoutConfig.colorScheme === 'dark'} inputId="theme1" onChange={(e) => changeColorScheme(e.value)}></RadioButton>
                    <label htmlFor="theme1">Dark</label>
                </div>
            </div>
        </>
    );
};

export default AppColorSchemeConfig;
