import { But<PERSON> } from 'primereact/button';
import { classNames } from 'primereact/utils';
import { useContext } from 'react';
import { LayoutContext } from '../../context/LayoutProvider';

const ScaleConfig = () => {
    const { layoutConfig, setLayoutConfig } = useContext(LayoutContext);
    const scales = [12, 13, 14, 15, 16];

    const decrementScale = () => {
        setLayoutConfig((prevState) => ({
            ...prevState,
            scale: prevState.scale - 1
        }));
    };

    const incrementScale = () => {
        setLayoutConfig((prevState) => ({
            ...prevState,
            scale: prevState.scale + 1
        }));
    };

    return (
        <>
            <h5>Scale</h5>
            <div className="flex align-items-center">
                <Button text rounded icon="pi pi-minus" onClick={decrementScale} className=" w-2rem h-2rem mr-2" disabled={layoutConfig.scale === scales[0]}></Button>
                <div className="flex gap-2 align-items-center">
                    {scales.map((s, i) => {
                        return (
                            <i
                                key={i}
                                className={classNames('pi pi-circle-fill text-300', {
                                    'text-primary-500': s === layoutConfig.scale
                                })}
                            ></i>
                        );
                    })}
                </div>
                <Button text rounded icon="pi pi-plus" onClick={incrementScale} className=" w-2rem h-2rem ml-2" disabled={layoutConfig.scale === scales[scales.length - 1]}></Button>
            </div>
        </>
    );
};

export default ScaleConfig;
