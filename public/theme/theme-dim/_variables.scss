$colors: (
    "blue": #2196f3,
    "green": #4caf50,
    "yellow": #fbc02d,
    "cyan": #00bcd4,
    "pink": #e91e63,
    "indigo": #3f51b5,
    "teal": #009688,
    "orange": #f57c00,
    "bluegray": #607d8b,
    "purple": #9c27b0,
    "red": #f44336,
    "primary": $primaryColor
) !default;

//shades
$shade000: rgba(255, 255, 255, 0.87) !default; //text color
$shade100: rgba(255, 255, 255, 0.6) !default; //text secondary color
$shade600: #304562 !default; //input bg, border, divider
$shade700: #283951 !default; //unused
$shade800: #1f2d40 !default; //elevated surface
$shade900: #17212f !default; //ground surface

$hoverBg: rgba(255, 255, 255, 0.03) !default;

//global
$fontFamily: "Nunito", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !default;
$fontSize: 1rem !default;
$fontWeight: normal !default;
$textColor: $shade000 !default;
$textSecondaryColor: $shade100 !default;
$borderRadius: 4px !default;
$transitionDuration: 0.2s !default;
$formElementTransition: background-color $transitionDuration, color $transitionDuration, border-color $transitionDuration, box-shadow $transitionDuration !default;
$actionIconTransition: background-color $transitionDuration, color $transitionDuration, box-shadow $transitionDuration !default;
$listItemTransition: box-shadow $transitionDuration !default;
$primeIconFontSize: 1rem !default;
$divider: 1px solid $shade600 !default;
$inlineSpacing: 0.5rem !default; //spacing between inline elements
$disabledOpacity: 0.4 !default; //opacity of a disabled item
$maskBg: rgba(0, 0, 0, 0.4) !default; //modal mask bg color
$loadingIconFontSize: 2rem !default;
$errorColor: #ef9a9a !default;

//scale
$scaleSM: 0.875 !default;
$scaleLG: 1.25 !default;

//focus
$focusOutlineColor: $primaryLightColor !default;
$focusOutline: 0 none !default;
$focusOutlineOffset: 0 !default;
$focusShadow: 0 0 0 1px $focusOutlineColor !default;

//action icons
$actionIconWidth: 2rem !default;
$actionIconHeight: 2rem !default;
$actionIconBg: transparent !default;
$actionIconBorder: 0 none !default;
$actionIconColor: $shade100 !default;
$actionIconHoverBg: $hoverBg !default;
$actionIconHoverBorderColor: transparent !default;
$actionIconHoverColor: $shade000 !default;
$actionIconBorderRadius: 50% !default;

//input field (e.g. inputtext, spinner, inputmask)
$inputPadding: 0.5rem 0.5rem !default;
$inputTextFontSize: 1rem !default;
$inputBg: $shade900 !default;
$inputTextColor: $shade000 !default;
$inputIconColor: $shade100 !default;
$inputBorder: 1px solid $shade600 !default;
$inputHoverBorderColor: $primaryColor !default;
$inputFocusBorderColor: $primaryColor !default;
$inputErrorBorderColor: $errorColor !default;
$inputPlaceholderTextColor: $shade100 !default;
$inputFilledBg: $shade600 !default;
$inputFilledHoverBg: $inputFilledBg !default;
$inputFilledFocusBg: $inputFilledBg !default;

//input groups
$inputGroupBg: $shade800 !default;
$inputGroupTextColor: $shade100 !default;
$inputGroupAddOnMinWidth: 2.357rem !default;

//input lists (e.g. dropdown, autocomplete, multiselect, orderlist)
$inputListBg: $shade800 !default;
$inputListTextColor: $shade000 !default;
$inputListBorder: $inputBorder !default;
$inputListPadding: 0.5rem 0 !default;
$inputListItemPadding: 0.5rem 1rem !default;
$inputListItemBg: transparent !default;
$inputListItemTextColor: $shade000 !default;
$inputListItemHoverBg: $hoverBg !default;
$inputListItemTextHoverColor: $shade000 !default;
$inputListItemBorder: 0 none !default;
$inputListItemBorderRadius: 0 !default;
$inputListItemMargin: 0 !default;
$inputListItemFocusShadow: inset 0 0 0 1px $focusOutlineColor !default;
$inputListHeaderPadding: 0.5rem 1rem !default;
$inputListHeaderMargin: 0 !default;
$inputListHeaderBg: $shade800 !default;
$inputListHeaderTextColor: $shade000 !default;
$inputListHeaderBorder: 0 none !default;

//inputs with overlays (e.g. autocomplete, dropdown, multiselect)
$inputOverlayBg: $inputListBg !default;
$inputOverlayHeaderBg: $inputListHeaderBg !default;
$inputOverlayBorder: 1px solid $shade600 !default;
$inputOverlayShadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12) !default;

//password
$passwordMeterBg: $shade600 !default;
$passwordWeakBg: #f48fb1 !default;
$passwordMediumBg: #ffe082 !default;
$passwordStrongBg: #c5e1a5 !default;

//button
$buttonPadding: 0.5rem 1rem !default;
$buttonIconOnlyWidth: 2.357rem !default;
$buttonIconOnlyPadding: 0.5rem 0 !default;
$buttonBg: $primaryColor !default;
$buttonTextColor: $primaryTextColor !default;
$buttonBorder: 1px solid $primaryColor !default;
$buttonHoverBg: $primaryDarkColor !default;
$buttonTextHoverColor: $primaryTextColor !default;
$buttonHoverBorderColor: $primaryDarkColor !default;
$buttonActiveBg: $primaryDarkerColor !default;
$buttonTextActiveColor: $primaryTextColor !default;
$buttonActiveBorderColor: $primaryDarkerColor !default;
$raisedButtonShadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12) !default;
$roundedButtonBorderRadius: 2rem !default;

$textButtonHoverBgOpacity: 0.04 !default;
$textButtonActiveBgOpacity: 0.16 !default;
$outlinedButtonBorder: 1px solid !default;
$plainButtonTextColor: $textSecondaryColor !default;
$plainButtonHoverBgColor: $hoverBg !default;
$plainButtonActiveBgColor: rgba(255, 255, 255, 0.16) !default;

$secondaryButtonBg: #78909c !default;
$secondaryButtonTextColor: #ffffff !default;
$secondaryButtonBorder: 1px solid $secondaryButtonBg !default;
$secondaryButtonHoverBg: scale-color($secondaryButtonBg, $lightness: -10%) !default;
$secondaryButtonTextHoverColor: $secondaryButtonTextColor !default;
$secondaryButtonHoverBorderColor: scale-color($secondaryButtonBg, $lightness: -10%) !default;
$secondaryButtonActiveBg: scale-color($secondaryButtonBg, $lightness: -20%) !default;
$secondaryButtonTextActiveColor: $secondaryButtonTextColor !default;
$secondaryButtonActiveBorderColor: scale-color($secondaryButtonBg, $lightness: -20%) !default;
$secondaryButtonFocusShadow: 0 0 0 1px scale-color($secondaryButtonBg, $lightness: 30%) !default;

$infoButtonBg: #81d4fa !default;
$infoButtonTextColor: #121212 !default;
$infoButtonBorder: 1px solid $infoButtonBg !default;
$infoButtonHoverBg: scale-color($infoButtonBg, $lightness: -10%) !default;
$infoButtonTextHoverColor: $infoButtonTextColor !default;
$infoButtonHoverBorderColor: scale-color($infoButtonBg, $lightness: -10%) !default;
$infoButtonActiveBg: scale-color($infoButtonBg, $lightness: -20%) !default;
$infoButtonTextActiveColor: $infoButtonTextColor !default;
$infoButtonActiveBorderColor: scale-color($infoButtonBg, $lightness: -20%) !default;
$infoButtonFocusShadow: 0 0 0 1px scale-color($infoButtonBg, $lightness: 30%) !default;

$successButtonBg: #c5e1a5 !default;
$successButtonTextColor: #121212 !default;
$successButtonBorder: 1px solid $successButtonBg !default;
$successButtonHoverBg: scale-color($successButtonBg, $lightness: -10%) !default;
$successButtonTextHoverColor: $successButtonTextColor !default;
$successButtonHoverBorderColor: scale-color($successButtonBg, $lightness: -10%) !default;
$successButtonActiveBg: scale-color($successButtonBg, $lightness: -20%) !default;
$successButtonTextActiveColor: $successButtonTextColor !default;
$successButtonActiveBorderColor: scale-color($successButtonBg, $lightness: -20%) !default;
$successButtonFocusShadow: 0 0 0 1px scale-color($successButtonBg, $lightness: 30%) !default;

$warningButtonBg: #ffe082 !default;
$warningButtonTextColor: #121212 !default;
$warningButtonBorder: 1px solid $warningButtonBg !default;
$warningButtonHoverBg: scale-color($warningButtonBg, $lightness: -10%) !default;
$warningButtonTextHoverColor: $warningButtonTextColor !default;
$warningButtonHoverBorderColor: scale-color($warningButtonBg, $lightness: -10%) !default;
$warningButtonActiveBg: scale-color($warningButtonBg, $lightness: -20%) !default;
$warningButtonTextActiveColor: $warningButtonTextColor !default;
$warningButtonActiveBorderColor: scale-color($warningButtonBg, $lightness: -20%) !default;
$warningButtonFocusShadow: 0 0 0 1px scale-color($warningButtonBg, $lightness: 30%) !default;

$helpButtonBg: #ce93d8 !default;
$helpButtonTextColor: #121212 !default;
$helpButtonBorder: 1px solid $helpButtonBg !default;
$helpButtonHoverBg: scale-color($helpButtonBg, $lightness: -10%) !default;
$helpButtonTextHoverColor: $helpButtonTextColor !default;
$helpButtonHoverBorderColor: scale-color($helpButtonBg, $lightness: -10%) !default;
$helpButtonActiveBg: scale-color($helpButtonBg, $lightness: -20%) !default;
$helpButtonTextActiveColor: $helpButtonTextColor !default;
$helpButtonActiveBorderColor: scale-color($helpButtonBg, $lightness: -20%) !default;
$helpButtonFocusShadow: 0 0 0 1px scale-color($helpButtonBg, $lightness: 30%) !default;

$dangerButtonBg: #f48fb1 !default;
$dangerButtonTextColor: #121212 !default;
$dangerButtonBorder: 1px solid $dangerButtonBg !default;
$dangerButtonHoverBg: scale-color($dangerButtonBg, $lightness: -10%) !default;
$dangerButtonTextHoverColor: $dangerButtonTextColor !default;
$dangerButtonHoverBorderColor: scale-color($dangerButtonBg, $lightness: -10%) !default;
$dangerButtonActiveBg: scale-color($dangerButtonBg, $lightness: -20%) !default;
$dangerButtonTextActiveColor: $dangerButtonTextColor !default;
$dangerButtonActiveBorderColor: scale-color($dangerButtonBg, $lightness: -20%) !default;
$dangerButtonFocusShadow: 0 0 0 1px scale-color($dangerButtonBg, $lightness: 30%) !default;

$linkButtonColor: $primaryColor !default;
$linkButtonHoverColor: $primaryColor !default;
$linkButtonTextHoverDecoration: underline !default;
$linkButtonFocusShadow: 0 0 0 0.1rem $focusOutlineColor !default;

//checkbox
$checkboxWidth: 20px !default;
$checkboxHeight: 20px !default;
$checkboxBorder: 2px solid $shade600 !default;
$checkboxIconFontSize: 14px !default;
$checkboxActiveBorderColor: $primaryColor !default;
$checkboxActiveBg: $primaryColor !default;
$checkboxIconActiveColor: $primaryTextColor !default;
$checkboxActiveHoverBg: $primaryDarkerColor !default;
$checkboxIconActiveHoverColor: $primaryTextColor !default;
$checkboxActiveHoverBorderColor: $primaryDarkerColor !default;

//radiobutton
$radiobuttonWidth: 20px !default;
$radiobuttonHeight: 20px !default;
$radiobuttonBorder: 2px solid $shade600 !default;
$radiobuttonIconSize: 12px !default;
$radiobuttonActiveBorderColor: $primaryColor !default;
$radiobuttonActiveBg: $primaryColor !default;
$radiobuttonIconActiveColor: $primaryTextColor !default;
$radiobuttonActiveHoverBg: $primaryDarkerColor !default;
$radiobuttonIconActiveHoverColor: $primaryTextColor !default;
$radiobuttonActiveHoverBorderColor: $primaryDarkerColor !default;

//colorpicker
$colorPickerPreviewWidth: 2rem !default;
$colorPickerPreviewHeight: 2rem !default;
$colorPickerBg: $shade800 !default;
$colorPickerBorder: 1px solid $shade600 !default;
$colorPickerHandleColor: #ffffff !default;

//togglebutton
$toggleButtonBg: $shade800 !default;
$toggleButtonBorder: 1px solid $shade600 !default;
$toggleButtonTextColor: $shade000 !default;
$toggleButtonIconColor: $shade100 !default;
$toggleButtonHoverBg: $hoverBg !default;
$toggleButtonHoverBorderColor: $shade600 !default;
$toggleButtonTextHoverColor: $shade000 !default;
$toggleButtonIconHoverColor: $shade100 !default;
$toggleButtonActiveBg: $primaryColor !default;
$toggleButtonActiveBorderColor: $primaryColor !default;
$toggleButtonTextActiveColor: $primaryTextColor !default;
$toggleButtonIconActiveColor: $primaryTextColor !default;
$toggleButtonActiveHoverBg: $primaryDarkColor !default;
$toggleButtonActiveHoverBorderColor: $primaryDarkColor !default;
$toggleButtonTextActiveHoverColor: $primaryTextColor !default;
$toggleButtonIconActiveHoverColor: $primaryTextColor !default;

//inplace
$inplacePadding: $inputPadding !default;
$inplaceHoverBg: $hoverBg !default;
$inplaceTextHoverColor: $shade000 !default;

//rating
$ratingIconFontSize: 1.143rem !default;
$ratingCancelIconColor: #f48fb1 !default;
$ratingCancelIconHoverColor: #f48fb1 !default;
$ratingStarIconOffColor: $shade000 !default;
$ratingStarIconOnColor: $primaryColor !default;
$ratingStarIconHoverColor: $primaryColor !default;

//slider
$sliderBg: $shade600 !default;
$sliderBorder: 0 none !default;
$sliderHorizontalHeight: 0.286rem !default;
$sliderVerticalWidth: 0.286rem !default;
$sliderHandleWidth: 1.143rem !default;
$sliderHandleHeight: 1.143rem !default;
$sliderHandleBg: $shade600 !default;
$sliderHandleBorder: 2px solid $primaryColor !default;
$sliderHandleBorderRadius: 50% !default;
$sliderHandleHoverBorderColor: $primaryColor !default;
$sliderHandleHoverBg: $primaryColor !default;
$sliderRangeBg: $primaryColor !default;

//calendar
$calendarTableMargin: 0.5rem 0 !default;
$calendarPadding: 0.5rem !default;
$calendarBg: $shade800 !default;
$calendarInlineBg: $calendarBg !default;
$calendarTextColor: $shade000 !default;
$calendarBorder: $inputListBorder !default;
$calendarOverlayBorder: $inputOverlayBorder !default;

$calendarHeaderPadding: 0.5rem !default;
$calendarHeaderBg: $shade800 !default;
$calendarInlineHeaderBg: $calendarBg !default;
$calendarHeaderBorder: 1px solid $shade600 !default;
$calendarHeaderTextColor: $shade000 !default;
$calendarHeaderFontWeight: 600 !default;
$calendarHeaderCellPadding: 0.5rem !default;
$calendarMonthYearHeaderHoverTextColor: $primaryColor !default;

$calendarCellDatePadding: 0.5rem !default;
$calendarCellDateWidth: 2.5rem !default;
$calendarCellDateHeight: 2.5rem !default;
$calendarCellDateBorderRadius: 50% !default;
$calendarCellDateBorder: 1px solid transparent !default;
$calendarCellDateHoverBg: $hoverBg !default;
$calendarCellDateTodayBg: transparent !default;
$calendarCellDateTodayBorderColor: transparent !default;
$calendarCellDateTodayTextColor: $primaryColor !default;

$calendarButtonBarPadding: 1rem 0 !default;
$calendarTimePickerPadding: 0.5rem !default;
$calendarTimePickerElementPadding: 0 0.5rem !default;
$calendarTimePickerTimeFontSize: 1.25rem !default;

$calendarBreakpoint: 769px !default;
$calendarCellDatePaddingSM: 0 !default;

//input switch
$inputSwitchWidth: 3rem !default;
$inputSwitchHeight: 1.75rem !default;
$inputSwitchBorderRadius: 30px !default;
$inputSwitchHandleWidth: 1.25rem !default;
$inputSwitchHandleHeight: 1.25rem !default;
$inputSwitchHandleBorderRadius: 50% !default;
$inputSwitchSliderPadding: 0.25rem !default;
$inputSwitchSliderOffBg: $shade600 !default;
$inputSwitchHandleOffBg: $shade100 !default;
$inputSwitchSliderOffHoverBg: $hoverBg !default;
$inputSwitchSliderOnBg: $primaryColor !default;
$inputSwitchSliderOnHoverBg: $primaryDarkColor !default;
$inputSwitchHandleOnBg: $shade000 !default;

//panel
$panelHeaderBorderColor: $shade600 !default;
$panelHeaderBorder: 1px solid $shade600 !default;
$panelHeaderBg: $shade800 !default;
$panelHeaderTextColor: $shade000 !default;
$panelHeaderFontWeight: 600 !default;
$panelHeaderPadding: 1rem !default;
$panelToggleableHeaderPadding: 0.5rem 1rem !default;

$panelHeaderHoverBg: $hoverBg !default;
$panelHeaderHoverBorderColor: $shade600 !default;
$panelHeaderTextHoverColor: $shade000 !default;

$panelContentBorderColor: $shade600 !default;
$panelContentBorder: 1px solid $shade600 !default;
$panelContentBg: $shade800 !default;
$panelContentEvenRowBg: rgba(255, 255, 255, 0.02) !default;
$panelContentTextColor: $shade000 !default;
$panelContentPadding: 1rem !default;

$panelFooterBorder: 1px solid $shade600 !default;
$panelFooterBg: $shade800 !default;
$panelFooterTextColor: $shade000 !default;
$panelFooterPadding: 0.5rem 1rem !default;

//accordion
$accordionSpacing: 0 !default;
$accordionHeaderBorder: $panelHeaderBorder !default;
$accordionHeaderBg: $panelHeaderBg !default;
$accordionHeaderTextColor: $panelHeaderTextColor !default;
$accordionHeaderFontWeight: $panelHeaderFontWeight !default;
$accordionHeaderPadding: $panelHeaderPadding !default;

$accordionHeaderHoverBg: $hoverBg !default;
$accordionHeaderHoverBorderColor: $shade600 !default;
$accordionHeaderTextHoverColor: $shade000 !default;

$accordionHeaderActiveBg: $panelHeaderBg !default;
$accordionHeaderActiveBorderColor: $shade600 !default;
$accordionHeaderTextActiveColor: $shade000 !default;

$accordionHeaderActiveHoverBg: $hoverBg !default;
$accordionHeaderActiveHoverBorderColor: $shade600 !default;
$accordionHeaderTextActiveHoverColor: $shade000 !default;

$accordionContentBorder: $panelContentBorder !default;
$accordionContentBg: $panelContentBg !default;
$accordionContentTextColor: $panelContentTextColor !default;
$accordionContentPadding: $panelContentPadding !default;

//tabview
$tabviewNavBorder: 1px solid $shade600 !default;
$tabviewNavBorderWidth: 0 0 2px 0 !default;
$tabviewNavBg: transparent !default;

$tabviewHeaderSpacing: 0 !default;
$tabviewHeaderBorder: solid $shade600 !default;
$tabviewHeaderBorderWidth: 0 0 2px 0 !default;
$tabviewHeaderBorderColor: transparent transparent $shade600 transparent !default;
$tabviewHeaderBg: $shade800 !default;
$tabviewHeaderTextColor: $shade100 !default;
$tabviewHeaderFontWeight: $panelHeaderFontWeight !default;
$tabviewHeaderPadding: $panelHeaderPadding !default;
$tabviewHeaderMargin: 0 0 -2px 0 !default;

$tabviewHeaderHoverBg: $shade800 !default;
$tabviewHeaderHoverBorderColor: $primaryColor !default;
$tabviewHeaderTextHoverColor: $shade000 !default;

$tabviewHeaderActiveBg: $shade800 !default;
$tabviewHeaderActiveBorderColor: $primaryColor !default;
$tabviewHeaderTextActiveColor: $primaryColor !default;

$tabviewContentBorder: 0 none !default;
$tabviewContentBg: $shade800 !default;
$tabviewContentTextColor: $shade000 !default;
$tabviewContentPadding: $panelContentPadding !default;

//upload
$fileUploadProgressBarHeight: 0.25rem !default;
$fileUploadContentPadding: 2rem 1rem !default;

//scrollpanel
$scrollPanelTrackBorder: 0 none !default;
$scrollPanelTrackBg: $shade600 !default;

//card
$cardBodyPadding: 1rem !default;
$cardTitleFontSize: 1.5rem !default;
$cardTitleFontWeight: 700 !default;
$cardSubTitleFontWeight: 700 !default;
$cardSubTitleColor: $shade100 !default;
$cardContentPadding: 1rem 0 !default;
$cardFooterPadding: 1rem 0 0 0 !default;
$cardShadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 1px 3px 0 rgba(0, 0, 0, 0.12) !default;

//editor
$editorToolbarBg: $panelHeaderBg !default;
$editorToolbarBorder: $panelHeaderBorder !default;
$editorToolbarPadding: $panelHeaderPadding !default;
$editorToolbarIconColor: $textSecondaryColor !default;
$editorToolbarIconHoverColor: $textColor !default;
$editorIconActiveColor: $primaryColor !default;
$editorContentBorder: $panelContentBorder !default;
$editorContentBg: $panelContentBg !default;

//paginator
$paginatorBg: $shade800 !default;
$paginatorTextColor: $shade100 !default;
$paginatorBorder: solid $shade600 !default;
$paginatorBorderWidth: 1px !default;
$paginatorPadding: 0.5rem 1rem !default;
$paginatorElementWidth: 2.286em !default;
$paginatorElementHeight: 2.286em !default;
$paginatorElementBg: transparent !default;
$paginatorElementBorder: 0 none !default;
$paginatorElementIconColor: $shade100 !default;
$paginatorElementHoverBg: $hoverBg !default;
$paginatorElementHoverBorderColor: transparent !default;
$paginatorElementIconHoverColor: $shade000 !default;
$paginatorElementBorderRadius: $borderRadius !default;
$paginatorElementMargin: 0.143rem !default;
$paginatorElementPadding: 0 !default;

//table
$tableHeaderBorder: 1px solid $shade600 !default;
$tableHeaderBorderWidth: 0 0 1px 0 !default;
$tableHeaderBg: $shade800 !default;
$tableHeaderTextColor: $shade100 !default;
$tableHeaderFontWeight: 600 !default;
$tableHeaderPadding: 1rem 1rem !default;

$tableHeaderCellPadding: 1rem 1rem !default;
$tableHeaderCellBg: $shade800 !default;
$tableHeaderCellTextColor: $shade000 !default;
$tableHeaderCellFontWeight: 600 !default;
$tableHeaderCellBorder: 1px solid $shade600 !default;
$tableHeaderCellBorderWidth: 0 0 1px 0 !default;
$tableHeaderCellHoverBg: $hoverBg !default;
$tableHeaderCellTextHoverColor: $shade000 !default;
$tableHeaderCellIconColor: $shade100 !default;
$tableHeaderCellIconHoverColor: $shade000 !default;
$tableHeaderCellHighlightBg: $shade800 !default;
$tableHeaderCellHighlightTextColor: $primaryColor !default;
$tableHeaderCellHighlightHoverBg: $hoverBg !default;
$tableHeaderCellHighlightTextHoverColor: $primaryColor !default;
$tableSortableColumnBadgeSize: 1.143rem !default;

$tableBodyRowBg: $shade800 !default;
$tableBodyRowTextColor: $shade000 !default;
$tableBodyRowEvenBg: rgba(255, 255, 255, 0.01) !default;
$tableBodyRowHoverBg: $hoverBg !default;
$tableBodyRowTextHoverColor: $shade000 !default;
$tableBodyCellBorder: 1px solid $shade600 !default;
$tableBodyCellBorderWidth: 0 0 1px 0 !default;
$tableBodyCellPadding: 1rem 1rem !default;

$tableFooterCellPadding: 1rem 1rem !default;
$tableFooterCellBg: $shade800 !default;
$tableFooterCellTextColor: $shade000 !default;
$tableFooterCellFontWeight: 600 !default;
$tableFooterCellBorder: 1px solid $shade600 !default;
$tableFooterCellBorderWidth: 0 0 1px 0 !default;
$tableResizerHelperBg: $primaryColor !default;
$tableDragHelperBg: rgba($primaryColor, 0.16) !default;

$tableFooterBorder: 1px solid $shade600 !default;
$tableFooterBorderWidth: 0 0 1px 0 !default;
$tableFooterBg: $shade800 !default;
$tableFooterTextColor: $shade000 !default;
$tableFooterFontWeight: 600 !default;
$tableFooterPadding: 1rem 1rem !default;

$tableCellContentAlignment: left !default;
$tableTopPaginatorBorderWidth: 1px 0 1px 0 !default;
$tableBottomPaginatorBorderWidth: 0 0 1px 0 !default;

$tableScaleSM: 0.5 !default;
$tableScaleLG: 1.25 !default;

//dataview
$dataViewContentPadding: 0 !default;
$dataViewContentBorder: 0 none !default;
$dataViewListItemBorder: solid $shade600 !default;
$dataViewListItemBorderWidth: 0 0 1px 0 !default;

//orderlist, picklist
$orderListBreakpoint: 960px !default;
$pickListBreakpoint: 960px !default;

//schedule
$fullCalendarEventBg: $primaryDarkColor !default;
$fullCalendarEventBorderColor: $primaryDarkColor !default;
$fullCalendarEventBorder: 1px solid $primaryDarkColor !default;
$fullCalendarEventTextColor: $primaryTextColor !default;

//tree
$treeContainerPadding: 0.286rem !default;
$treeNodePadding: 0.143rem !default;
$treeNodeContentPadding: 0.5rem !default;
$treeNodeChildrenPadding: 0 0 0 1rem !default;
$treeNodeIconColor: $shade100 !default;

//timeline
$timelineVerticalEventContentPadding: 0 1rem !default;
$timelineHorizontalEventContentPadding: 1rem 0 !default;
$timelineEventMarkerWidth: 1rem !default;
$timelineEventMarkerHeight: 1rem !default;
$timelineEventMarkerBorderRadius: 50% !default;
$timelineEventMarkerBorder: 2px solid $primaryColor !default;
$timelineEventMarkerBackground: $shade800 !default;
$timelineEventConnectorSize: 2px !default;
$timelineEventColor: $shade600 !default;

//org chart
$organizationChartConnectorColor: $shade100 !default;

//message
$messageMargin: 1rem 0 !default;
$messagePadding: 1rem 1.5rem !default;
$messageBorderWidth: 0 0 0 6px !default;
$messageIconFontSize: 1.5rem !default;
$messageTextFontSize: 1rem !default;
$messageTextFontWeight: 500 !default;

//inline message
$inlineMessagePadding: $inputPadding !default;
$inlineMessageMargin: 0 !default;
$inlineMessageIconFontSize: 1rem !default;
$inlineMessageTextFontSize: 1rem !default;
$inlineMessageBorderWidth: 1px !default;

//toast
$toastIconFontSize: 2rem !default;
$toastMessageTextMargin: 0 0 0 1rem !default;
$toastMargin: 0 0 1rem 0 !default;
$toastPadding: 1rem !default;
$toastBorderWidth: 0 0 0 6px !default;
$toastShadow: none !default;
$toastOpacity: 0.9 !default;
$toastTitleFontWeight: 700 !default;
$toastDetailMargin: $inlineSpacing 0 0 0 !default;

//severities
$infoMessageBg: #b3e5fc !default;
$infoMessageBorder: solid scale-color($infoMessageBg, $lightness: -50%) !default;
$infoMessageTextColor: scale-color($infoMessageBg, $lightness: -75%) !default;
$infoMessageIconColor: scale-color($infoMessageBg, $lightness: -75%) !default;
$successMessageBg: #c8e6c9 !default;
$successMessageBorder: solid scale-color($successMessageBg, $lightness: -50%) !default;
$successMessageTextColor: scale-color($successMessageBg, $lightness: -75%) !default;
$successMessageIconColor: scale-color($successMessageBg, $lightness: -75%) !default;
$warningMessageBg: #ffecb3 !default;
$warningMessageBorder: solid scale-color($warningMessageBg, $lightness: -50%) !default;
$warningMessageTextColor: scale-color($warningMessageBg, $lightness: -75%) !default;
$warningMessageIconColor: scale-color($warningMessageBg, $lightness: -75%) !default;
$errorMessageBg: #ffcdd2 !default;
$errorMessageBorder: solid scale-color($errorMessageBg, $lightness: -50%) !default;
$errorMessageTextColor: scale-color($errorMessageBg, $lightness: -75%) !default;
$errorMessageIconColor: scale-color($errorMessageBg, $lightness: -75%) !default;

//overlays
$overlayContentBorder: 1px solid $shade600 !default;
$overlayContentBg: $panelContentBg !default;
$overlayContainerShadow: 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12) !default;

//dialog
$dialogHeaderBg: $shade800 !default;
$dialogHeaderBorder: 0 none !default;
$dialogHeaderTextColor: $shade000 !default;
$dialogHeaderFontWeight: 600 !default;
$dialogHeaderFontSize: 1.25rem !default;
$dialogHeaderPadding: 1.5rem !default;
$dialogContentPadding: 0 1.5rem 2rem 1.5rem !default;
$dialogFooterBorder: 0 none !default;
$dialogFooterPadding: 0 1.5rem 1.5rem 1.5rem !default;

//confirmpopup
$confirmPopupContentPadding: $panelContentPadding;
$confirmPopupFooterPadding: 0 1rem 1rem 1rem;

//tooltip
$tooltipBg: $shade600 !default;
$tooltipTextColor: $shade000 !default;
$tooltipPadding: $inputPadding !default;

//steps
$stepsItemBg: transparent !default;
$stepsItemBorder: 0 none !default;
$stepsItemTextColor: $shade100 !default;
$stepsItemNumberWidth: 2rem !default;
$stepsItemNumberHeight: 2rem !default;
$stepsItemNumberFontSize: 1.143rem !default;
$stepsItemNumberColor: $shade000 !default;
$stepsItemNumberBorderRadius: 50% !default;
$stepsItemActiveFontWeight: 600 !default;

//progressbar
$progressBarHeight: 1.5rem !default;
$progressBarBorder: 0 none !default;
$progressBarBg: $shade600 !default;
$progressBarValueBg: $primaryColor !default;
$progressBarValueTextColor: $primaryTextColor !default;

//menu (e.g. menu, menubar, tieredmenu)
$menuWidth: 12.5rem !default;
$menuBg: $shade800 !default;
$menuBorder: 1px solid $shade600 !default;
$menuTextColor: $shade000 !default;
$menuitemPadding: 0.75rem 1rem !default;
$menuitemBorderRadius: 0 !default;
$menuitemTextColor: $shade000 !default;
$menuitemIconColor: $shade100 !default;
$menuitemTextHoverColor: $shade000 !default;
$menuitemIconHoverColor: $shade000 !default;
$menuitemHoverBg: $hoverBg !default;
$menuitemTextActiveColor: $shade000 !default;
$menuitemIconActiveColor: $shade000 !default;
$menuitemActiveBg: $shade900 !default;
$menuitemSubmenuIconFontSize: 0.875rem !default;
$submenuHeaderMargin: 0 !default;
$submenuHeaderPadding: 0.75rem 1rem !default;
$submenuHeaderBg: $shade800 !default;
$submenuHeaderTextColor: $shade000 !default;
$submenuHeaderBorderRadius: 0 !default;
$submenuHeaderFontWeight: 600 !default;
$overlayMenuBg: $menuBg !default;
$overlayMenuBorder: 1px solid $shade600 !default;
$overlayMenuShadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12) !default;
$verticalMenuPadding: 0.25rem 0 !default;
$menuSeparatorMargin: 0.25rem 0 !default;

$breadcrumbPadding: 1rem !default;
$breadcrumbBg: $menuBg !default;
$breadcrumbBorder: $menuBorder !default;
$breadcrumbItemTextColor: $menuitemTextColor !default;
$breadcrumbItemIconColor: $menuitemIconColor !default;
$breadcrumbLastItemTextColor: $menuitemTextColor !default;
$breadcrumbLastItemIconColor: $menuitemIconColor !default;
$breadcrumbSeparatorColor: $menuitemTextColor !default;

$horizontalMenuPadding: 0.5rem !default;
$horizontalMenuBg: $menuBg !default;
$horizontalMenuBorder: $menuBorder !default;
$horizontalMenuTextColor: $menuTextColor !default;
$horizontalMenuRootMenuitemPadding: $menuitemPadding !default;
$horizontalMenuRootMenuitemBorderRadius: $borderRadius !default;
$horizontalMenuRootMenuitemTextColor: $menuitemTextColor !default;
$horizontalMenuRootMenuitemIconColor: $menuitemIconColor !default;
$horizontalMenuRootMenuitemTextHoverColor: $menuitemTextHoverColor !default;
$horizontalMenuRootMenuitemIconHoverColor: $menuitemIconHoverColor !default;
$horizontalMenuRootMenuitemHoverBg: $menuitemHoverBg !default;
$horizontalMenuRootMenuitemTextActiveColor: $menuitemTextActiveColor !default;
$horizontalMenuRootMenuitemIconActiveColor: $menuitemIconActiveColor !default;
$horizontalMenuRootMenuitemActiveBg: $menuitemActiveBg !default;

//badge and tag
$badgeBg: $primaryColor !default;
$badgeTextColor: $primaryTextColor !default;
$badgeMinWidth: 1.5rem !default;
$badgeHeight: 1.5rem !default;
$badgeFontWeight: 700 !default;
$badgeFontSize: 0.75rem !default;

$tagPadding: 0.25rem 0.4rem !default;

//carousel
$carouselIndicatorsPadding: 1rem !default;
$carouselIndicatorBg: $shade600 !default;
$carouselIndicatorHoverBg: $hoverBg !default;
$carouselIndicatorBorderRadius: 0 !default;
$carouselIndicatorWidth: 2rem !default;
$carouselIndicatorHeight: 0.5rem !default;

//galleria
$galleriaMaskBg: rgba(0, 0, 0, 0.9) !default;
$galleriaCloseIconMargin: 0.5rem !default;
$galleriaCloseIconFontSize: 2rem !default;
$galleriaCloseIconBg: transparent !default;
$galleriaCloseIconColor: #f8f9fa !default;
$galleriaCloseIconHoverBg: rgba(255, 255, 255, 0.1) !default;
$galleriaCloseIconHoverColor: #f8f9fa !default;
$galleriaCloseIconWidth: 4rem !default;
$galleriaCloseIconHeight: 4rem !default;
$galleriaCloseIconBorderRadius: 50% !default;

$galleriaItemNavigatorBg: transparent !default;
$galleriaItemNavigatorColor: #f8f9fa !default;
$galleriaItemNavigatorMargin: 0 0.5rem !default;
$galleriaItemNavigatorFontSize: 2rem !default;
$galleriaItemNavigatorHoverBg: rgba(255, 255, 255, 0.1) !default;
$galleriaItemNavigatorHoverColor: #f8f9fa !default;
$galleriaItemNavigatorWidth: 4rem !default;
$galleriaItemNavigatorHeight: 4rem !default;
$galleriaItemNavigatorBorderRadius: $borderRadius !default;

$galleriaCaptionBg: rgba(0, 0, 0, 0.5) !default;
$galleriaCaptionTextColor: #f8f9fa !default;
$galleriaCaptionPadding: 1rem !default;

$galleriaIndicatorsPadding: 1rem !default;
$galleriaIndicatorBg: $shade600 !default;
$galleriaIndicatorHoverBg: rgba(255, 255, 255, 0.1) !default;
$galleriaIndicatorBorderRadius: 50% !default;
$galleriaIndicatorWidth: 1rem !default;
$galleriaIndicatorHeight: 1rem !default;
$galleriaIndicatorsBgOnItem: rgba(0, 0, 0, 0.5) !default;
$galleriaIndicatorBgOnItem: rgba(255, 255, 255, 0.4) !default;
$galleriaIndicatorHoverBgOnItem: rgba(255, 255, 255, 0.6) !default;

$galleriaThumbnailContainerBg: rgba(0, 0, 0, 0.9) !default;
$galleriaThumbnailContainerPadding: 1rem 0.25rem !default;
$galleriaThumbnailNavigatorBg: transparent !default;
$galleriaThumbnailNavigatorColor: #f8f9fa !default;
$galleriaThumbnailNavigatorHoverBg: rgba(255, 255, 255, 0.1) !default;
$galleriaThumbnailNavigatorHoverColor: #f8f9fa !default;
$galleriaThumbnailNavigatorBorderRadius: 50% !default;
$galleriaThumbnailNavigatorWidth: 2rem !default;
$galleriaThumbnailNavigatorHeight: 2rem !default;

//divider
$dividerHorizontalMargin: 1rem 0;
$dividerHorizontalPadding: 0 1rem;
$dividerVerticalMargin: 0 1rem;
$dividerVerticalPadding: 1rem 0;
$dividerSize: 1px;
$dividerColor: $shade600;

//avatar
$avatarBg: $shade600;
$avatarTextColor: $textColor;

//chip
$chipBg: $shade600;
$chipTextColor: $textColor;
$chipBorderRadius: 16px;

//scrollTop
$scrollTopBg: $highlightBg;
$scrollTopHoverBg: scale-color($highlightBg, $alpha: 24%);
$scrollTopWidth: 3rem;
$scrollTopHeight: 3rem;
$scrollTopBorderRadius: 50%;
$scrollTopFontSize: 1.5rem;
$scrollTopTextColor: $highlightTextColor;

//skeleton
$skeletonBg: rgba(255, 255, 255, 0.06);
$skeletonAnimationBg: rgba(255, 255, 255, 0.04);

//splitter
$splitterGutterBg: rgba(255, 255, 255, 0.03);
$splitterGutterHandleBg: $shade600;

//speeddial
$speedDialButtonWidth: 4rem;
$speedDialButtonHeight: 4rem;
$speedDialButtonIconFontSize: 1.3rem;
$speedDialActionWidth: 3rem;
$speedDialActionHeight: 3rem;
$speedDialActionBg: $shade000;
$speedDialActionHoverBg: $shade100;
$speedDialActionTextColor: $shade900;
$speedDialActionTextHoverColor: $shade900;

//dock
$dockActionWidth: 4rem;
$dockActionHeight: 4rem;
$dockItemPadding: 0.5rem;
$dockCurrentItemMargin: 1.5rem;
$dockFirstItemsMargin: 1.3rem;
$dockSecondItemsMargin: 0.9rem;
$dockBg: rgba(255, 255, 255, 0.1);
$dockBorder: 1px solid rgba(255, 255, 255, 0.2);
$dockPadding: 0.5rem 0.5rem;
$dockBorderRadius: 0.5rem;

//image
$imageMaskBg: rgba(0, 0, 0, 0.9) !default;
$imagePreviewToolbarPadding: 1rem !default;
$imagePreviewIndicatorColor: #f8f9fa !default;
$imagePreviewIndicatorBg: rgba(0, 0, 0, 0.5) !default;
$imagePreviewActionIconBg: transparent !default;
$imagePreviewActionIconColor: #f8f9fa !default;
$imagePreviewActionIconHoverBg: rgba(255, 255, 255, 0.1) !default;
$imagePreviewActionIconHoverColor: #f8f9fa !default;
$imagePreviewActionIconWidth: 3rem !default;
$imagePreviewActionIconHeight: 3rem !default;
$imagePreviewActionIconFontSize: 1.5rem !default;
$imagePreviewActionIconBorderRadius: 50% !default;

:root {
    --surface-a: #{$shade800};
    --surface-b: #{$shade900};
    --surface-c: #{$hoverBg};
    --surface-d: #{$shade600};
    --surface-e: #{$shade800};
    --surface-f: #{$shade800};
    --text-color: #{$shade000};
    --text-color-secondary: #{$shade100};
    --primary-color: #{$primaryColor};
    --primary-color-text: #{$primaryTextColor};
    --font-family: #{$fontFamily};
    --surface-0: #17212f;
    --surface-50: #2e3744;
    --surface-100: #454d59;
    --surface-200: #5d646d;
    --surface-300: #747a82;
    --surface-400: #8b9097;
    --surface-500: #a2a6ac;
    --surface-600: #b9bcc1;
    --surface-700: #d1d3d5;
    --surface-800: #e8e9ea;
    --surface-900: #ffffff;
    --gray-50: #e8e9ea;
    --gray-100: #d1d3d5;
    --gray-200: #b9bcc1;
    --gray-300: #a2a6ac;
    --gray-400: #8b9097;
    --gray-500: #747a82;
    --gray-600: #5d646d;
    --gray-700: #454d59;
    --gray-800: #2e3744;
    --gray-900: #17212f;
    --content-padding: #{$panelContentPadding};
    --inline-spacing: #{$inlineSpacing};
    --border-radius: #{$borderRadius};
    --surface-ground: #17212f;
    --surface-section: #17212f;
    --surface-card: #1f2d40;
    --surface-overlay: #1f2d40;
    --surface-border: #304562;
    --surface-hover: rgba(255, 255, 255, 0.03);
    --maskbg: #{$maskBg};
}
