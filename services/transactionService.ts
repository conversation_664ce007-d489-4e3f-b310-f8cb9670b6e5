import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { ApiStatus, ITransactionService } from '../types/demo';
import type { Demo } from '../types/types';
import { DevTransactionService } from './transaction/DevTransactionService';
import { ProdTransactionService } from './transaction/ProdTransactionService';

interface TransactionState {
    transactions: Demo.Transaction[];
    status: ApiStatus;
    error: string | null;
}

const initialState: TransactionState = {
    transactions: [],
    status: 'IDLE',
    error: null
};

const service: ITransactionService = process.env.NODE_ENV === 'production' ? new ProdTransactionService() : new DevTransactionService();

export const load = createAsyncThunk<Demo.Transaction[], Demo.Customer[]>('transaction/load', async (customers, { rejectWithValue }) => {
    try {
        return await service.loadTransactions(customers);
    } catch (err: any) {
        return rejectWithValue(err.response?.data || 'Failed to load transactions');
    }
});

export const add = createAsyncThunk<Demo.Transaction, Demo.Transaction>('transaction/save', async (transaction, { rejectWithValue }) => {
    try {
        return await service.addTransaction(transaction);
    } catch (err: any) {
        return rejectWithValue(err.response?.data || 'Failed to add transaction');
    }
});

export const update = createAsyncThunk<Demo.Transaction, Demo.Transaction>('transaction/update', async (transaction, { rejectWithValue }) => {
    try {
        return await service.updateTransaction(transaction);
    } catch (err: any) {
        return rejectWithValue(err.response?.data || 'Failed to update transaction');
    }
});

export const remove = createAsyncThunk<string, string>('transaction/delete', async (uuid, { rejectWithValue }) => {
    try {
        return await service.removeTransaction(uuid);
    } catch (err: any) {
        return rejectWithValue(err.response?.data || 'Failed to delete transaction');
    }
});

const transactionSlice = createSlice({
    name: 'transaction',
    initialState,
    reducers: {},
    extraReducers: () => {}
});

export const selectTransactions = (state: any) => state.transaction.transactions;
export const selectStatus = (state: any) => state.transaction.status;
export const selectError = (state: any) => state.transaction.error;

export default transactionSlice.reducer;
