import { Demo, ICustomerService } from '../../types/demo';


const API_BASE_URL = process.env.API_BASE_URL;

export class ProdCustomerService implements ICustomerService {

    async loadCustomers(): Promise<Demo.Customer[]> {
        const res = await fetch(`${API_BASE_URL}/transactions.json`, {
            headers: { 'Cache-Control': 'no-cache' }
        });
        const d = await res.json();
        return d.data as Demo.Customer[];
    }

    async addCustomer(customer: Demo.Customer): Promise<Demo.Customer> {
        return new Promise((resolve) => setTimeout(() => resolve(customer), 1000));
    }

    async updateCustomer(customer: Demo.Customer): Promise<Demo.Customer> {
        return new Promise((resolve) => setTimeout(() => resolve(customer), 1000));
    }

    async removeCustomer(uuid: string): Promise<string> {
        return new Promise((resolve) => setTimeout(() => resolve(uuid), 1000));
    }
}
