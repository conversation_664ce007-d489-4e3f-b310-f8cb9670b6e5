import { Demo, ICustomerService } from '../../types/demo';

export class DevCustomerService implements ICustomerService {
    async loadCustomers(): Promise<Demo.Customer[]> {
        try {
            console.log('Fetching customers');
            const response = await fetch('/api/admin/customer');

            if (!response.ok) {
                throw new Error(`Failed to fetch customers: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Received customer data:', data);

            return Array.isArray(data) ? data : [];
        } catch (error) {
            console.error('Failed to load customers:', error);
            throw new Error(`Failed to load customers: ${error.message}`);
        }
    }

    async addCustomer(customer: Demo.Customer): Promise<Demo.Customer> {
        try {
            console.log('Adding customer:', customer);
            const response = await fetch('/api/admin/customer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(customer),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to add customer: ${errorData.message || response.statusText}`);
            }

            const data = await response.json();
            console.log('Customer added successfully:', data);
            return data;
        } catch (error) {
            console.error('Failed to add customer:', error);
            throw new Error(`Failed to add customer: ${error.message}`);
        }
    }

    async updateCustomer(customer: Demo.Customer): Promise<Demo.Customer> {
        try {
            console.log('Updating customer:', customer);
            const response = await fetch(`/api/admin/customer/${customer.uuid}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(customer),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to update customer: ${errorData.message || response.statusText}`);
            }

            const data = await response.json();
            console.log('Customer updated successfully:', data);
            return data;
        } catch (error) {
            console.error('Failed to update customer:', error);
            throw new Error(`Failed to update customer: ${error.message}`);
        }
    }

    async removeCustomer(uuid: string): Promise<string> {
        try {
            console.log('Removing customer:', uuid);
            const response = await fetch(`/api/admin/customer/${uuid}`, {
                method: 'DELETE',
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to remove customer: ${errorData.message || response.statusText}`);
            }

            console.log('Customer removed successfully');
            return uuid;
        } catch (error) {
            console.error('Failed to remove customer:', error);
            throw new Error(`Failed to remove customer: ${error.message}`);
        }
    }
}
