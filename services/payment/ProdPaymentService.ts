import { Demo, IPaymentService } from '../../types/demo';

const API_BASE_URL = process.env.API_BASE_URL;

export class ProdPaymentService implements IPaymentService {

    async loadPayments(): Promise<Demo.Payment[]> {
        const res = await fetch(`${API_BASE_URL}/transactions.json`, {
            headers: { 'Cache-Control': 'no-cache' }
        });
        const d = await res.json();
        return d.data as Demo.Payment[];
    }

    async addPayment(transaction: Demo.Payment): Promise<Demo.Payment> {
        console.log('Adding transaction (simulated):', transaction);
        return new Promise((resolve) => setTimeout(() => resolve(transaction), 1000));
    }

    async updatePayment(transaction: Demo.Payment): Promise<Demo.Payment> {
        console.log('Updating transaction (simulated):', transaction);
        return new Promise((resolve) => setTimeout(() => resolve(transaction), 1000));
    }

    async removePayment(uuid: string): Promise<string> {
        console.log('Removing transaction (simulated):', uuid);
        return new Promise((resolve) => setTimeout(() => resolve(uuid), 1000));
    }
}
