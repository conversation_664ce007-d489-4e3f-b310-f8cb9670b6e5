import { Demo, IPaymentService } from '../../types/demo';

export class DevPaymentService implements IPaymentService {
    async loadPayments(transactions: Demo.Transaction[]): Promise<Demo.Payment[]> {
        try {
            const transactionIds = transactions.map((t) => (typeof t === 'string' ? t : t.uuid)).join(',');
            const queryParam = transactionIds ? `?transaction=${transactionIds}` : '';

            console.log('Fetching payments with query:', queryParam);
            const response = await fetch(`/api/admin/payment${queryParam}`);

            if (!response.ok) {
                throw new Error(`Failed to fetch payments: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Received payment data:', data);

            return Array.isArray(data) ? data : [];
        } catch (error) {
            console.error('Failed to load payments:', error);
            throw new Error(`Failed to load payments: ${error.message}`);
        }
    }

    async addPayment(payment: Demo.Payment): Promise<Demo.Payment> {
        try {
            console.log('Adding payment:', payment);
            const response = await fetch('/api/admin/payment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payment),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to add payment: ${errorData.message || response.statusText}`);
            }

            const data = await response.json();
            console.log('Payment added successfully:', data);
            return data;
        } catch (error) {
            console.error('Failed to add payment:', error);
            throw new Error(`Failed to add payment: ${error.message}`);
        }
    }

    async updatePayment(payment: Demo.Payment): Promise<Demo.Payment> {
        try {
            console.log('Updating payment:', payment);
            const response = await fetch(`/api/admin/payment/${payment.uuid}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payment),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to update payment: ${errorData.message || response.statusText}`);
            }

            const data = await response.json();
            console.log('Payment updated successfully:', data);
            return data;
        } catch (error) {
            console.error('Failed to update payment:', error);
            throw new Error(`Failed to update payment: ${error.message}`);
        }
    }

    async removePayment(uuid: string): Promise<string> {
        try {
            console.log('Removing payment:', uuid);
            const response = await fetch(`/api/admin/payment/${uuid}`, {
                method: 'DELETE',
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to remove payment: ${errorData.message || response.statusText}`);
            }

            console.log('Payment removed successfully');
            return uuid;
        } catch (error) {
            console.error('Failed to remove payment:', error);
            throw new Error(`Failed to remove payment: ${error.message}`);
        }
    }
}
