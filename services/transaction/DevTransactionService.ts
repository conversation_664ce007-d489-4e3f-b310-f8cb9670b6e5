import { ITransactionService } from '../../types/demo';
import type { Demo } from '../../types/types';

export class DevTransactionService implements ITransactionService {
    async loadTransactions(customers: Demo.Customer[]): Promise<Demo.Transaction[]> {
        try {
            const customerIds = customers.map((c) => (typeof c === 'string' ? c : c.uuid)).join(',');
            const queryParam = customerIds ? `?customers=${customerIds}` : '';

            console.log('Fetching transactions with query:', queryParam);
            const response = await fetch(`/api/admin/transaction${queryParam}`);

            if (!response.ok) {
                throw new Error(`Failed to fetch transactions: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Received transaction data:', data);

            return Array.isArray(data) ? data : [];
        } catch (error) {
            console.error('Failed to load transactions:', error);
            throw new Error(`Failed to load transactions: ${error.message}`);
        }
    }

    async addTransaction(transaction: Demo.Transaction): Promise<Demo.Transaction> {
        try {
            console.log('Adding transaction:', transaction);
            const response = await fetch('/api/admin/transaction', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(transaction),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to add transaction: ${errorData.message || response.statusText}`);
            }

            const data = await response.json();
            console.log('Transaction added successfully:', data);
            return data;
        } catch (error) {
            console.error('Failed to add transaction:', error);
            throw new Error(`Failed to add transaction: ${error.message}`);
        }
    }

    async updateTransaction(transaction: Demo.Transaction): Promise<Demo.Transaction> {
        try {
            console.log('Updating transaction:', transaction);
            const response = await fetch(`/api/admin/transaction/${transaction.uuid}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(transaction),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to update transaction: ${errorData.message || response.statusText}`);
            }

            const data = await response.json();
            console.log('Transaction updated successfully:', data);
            return data;
        } catch (error) {
            console.error('Failed to update transaction:', error);
            throw new Error(`Failed to update transaction: ${error.message}`);
        }
    }

    async removeTransaction(uuid: string): Promise<string> {
        try {
            console.log('Removing transaction:', uuid);
            const response = await fetch(`/api/admin/transaction/${uuid}`, {
                method: 'DELETE',
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to remove transaction: ${errorData.message || response.statusText}`);
            }

            console.log('Transaction removed successfully');
            return uuid;
        } catch (error) {
            console.error('Failed to remove transaction:', error);
            throw new Error(`Failed to remove transaction: ${error.message}`);
        }
    }
}
