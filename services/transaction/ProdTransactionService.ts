import { Demo, ITransactionService } from '../../types/demo';

const API_BASE_URL = process.env.API_BASE_URL;

export class ProdTransactionService implements ITransactionService {

    async loadTransactions(): Promise<Demo.Transaction[]> {
        const res = await fetch(`${API_BASE_URL}/transactions.json`, {
            headers: { 'Cache-Control': 'no-cache' }
        });
        const d = await res.json();
        return d.data as Demo.Transaction[];
    }

    async addTransaction(transaction: Demo.Transaction): Promise<Demo.Transaction> {
        console.log('Adding transaction (simulated):', transaction);
        return new Promise((resolve) => setTimeout(() => resolve(transaction), 1000));
    }

    async updateTransaction(transaction: Demo.Transaction): Promise<Demo.Transaction> {
        console.log('Updating transaction (simulated):', transaction);
        return new Promise((resolve) => setTimeout(() => resolve(transaction), 1000));
    }

    async removeTransaction(uuid: string): Promise<string> {
        console.log('Removing transaction (simulated):', uuid);
        return new Promise((resolve) => setTimeout(() => resolve(uuid), 1000));
    }
}
