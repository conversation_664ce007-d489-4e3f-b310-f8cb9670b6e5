import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { ApiStatus, IPaymentService } from '../types/demo';
import type { Demo } from '../types/types';
import { DevPaymentService } from './payment/DevPaymentService';
import { ProdPaymentService } from './payment/ProdPaymentService';

interface PaymentState {
    payments: Demo.Payment[];
    status: ApiStatus;
    error: string | null;
}

const initialState: PaymentState = {
    payments: [],
    status: 'IDLE',
    error: null
};

const service: IPaymentService = process.env.NODE_ENV === 'production' ? new ProdPaymentService() : new DevPaymentService();

export const load = createAsyncThunk<Demo.Payment[], Demo.Transaction[]>('payment/load', async (transactions, { rejectWithValue }) => {
    try {
        return await service.loadPayments(transactions);
    } catch (err: any) {
        return rejectWithValue(err.response?.data || 'Failed to load payments');
    }
});

export const add = createAsyncThunk<Demo.Payment, Demo.Payment>('payment/save', async (payment, { rejectWithValue }) => {
    try {
        return await service.addPayment(payment);
    } catch (err: any) {
        return rejectWithValue(err.response?.data || 'Failed to add payment');
    }
});

export const update = createAsyncThunk<Demo.Payment, Demo.Payment>('payment/update', async (payment, { rejectWithValue }) => {
    try {
        return await service.updatePayment(payment);
    } catch (err: any) {
        return rejectWithValue(err.response?.data || 'Failed to update payment');
    }
});

export const remove = createAsyncThunk<string, string>('payment/delete', async (uuid, { rejectWithValue }) => {
    try {
        return await service.removePayment(uuid);
    } catch (err: any) {
        return rejectWithValue(err.response?.data || 'Failed to delete payment');
    }
});

const paymentSlice = createSlice({
    name: 'payment',
    initialState,
    reducers: {},
    extraReducers: (builder) => {}
});

export const selectPayments = (state: any) => state.payment.payments;
export const selectStatus = (state: any) => state.payment.status;
export const selectError = (state: any) => state.payment.error;

export default paymentSlice.reducer;
