import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { ApiStatus, ICustomerService } from '../types/demo';
import type { Demo } from '../types/types';
import { DevCustomerService } from './customer/DevCustomerService';
import { ProdCustomerService } from './customer/ProdCustomerService';

interface CustomerState {
    customers: Demo.Customer[];
    status: ApiStatus;
    error: string | null;
}
const initialState: CustomerState = {
    customers: [],
    status: 'IDLE',
    error: null
};

const service: ICustomerService = process.env.NODE_ENV === 'production' ? new ProdCustomerService() : new DevCustomerService();

export const load = createAsyncThunk<Demo.Customer[]>('users', async () => {
    try {
        return await service.loadCustomers();
    } catch (err) {
        throw new Error(err.response?._customers || 'Failed to load customers');
    }
});

export const add = createAsyncThunk<Demo.Customer, Demo.Customer>('customer/save', async (customer: Demo.Customer, { rejectWithValue }) => {
    try {
        return (await service.addCustomer(customer)) as Demo.Customer;
    } catch (err) {
        return rejectWithValue(err.response._customers);
    }
});

export const update = createAsyncThunk<Demo.Customer, Demo.Customer>('customer/update', async (customer: Demo.Customer, { rejectWithValue }) => {
    try {
        return (await service.updateCustomer(customer)) as Demo.Customer;
    } catch (err) {
        return rejectWithValue(err.response._customers);
    }
});

export const remove = createAsyncThunk<string, string>('customer/delete', async (uuid: string, { rejectWithValue }) => {
    try {
        return await service.removeCustomer(uuid);
    } catch (err) {
        return rejectWithValue(err.response._customers);
    }
});

const customerSlice = createSlice({
    name: 'customer',
    initialState,
    reducers: {},
    extraReducers: (builder) => {}
});

export const selectCustomers = (state: any) => state.customer.customers;
export const selectStatus = (state: any) => state.customer.status;
export const selectError = (state: any) => state.customer.error;

export default customerSlice.reducer;
