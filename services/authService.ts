import { createSlice } from '@reduxjs/toolkit';

const authSlice = createSlice({
    name: 'auth',
    initialState: {user: null, token: null},
    reducers: {
        login(state, action) {
            const { user, accessToken } = action.payload;
            state.user = user;
            state.token = accessToken;
        },
        refresh(state, action) {
            const { accessToken } = action.payload;
            state.token = accessToken;
        },
        logout(state, action) {
            state.user = null;
            state.token = null;
        }
    },
});

export const { login, logout } = authSlice.actions;

export const selectAuth = (state) => state.auth.user;
export const selectToken = (state) => state.auth.token;

export default authSlice.reducer;