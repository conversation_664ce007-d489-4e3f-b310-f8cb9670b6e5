'use client';

import { FormEvent, useState } from 'react';
import { useRouter } from 'next/navigation';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { OAuthSignInButton } from '../../../../components/buttons';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const router = useRouter();

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password }),
    });

    if (response.ok) {
      router.push('/profile');  // Redirect after successful login
    } else {
      console.error('Login failed');
    }
  };

  return (
    <>
      <div className="flex h-screen">
        <div className="w-full lg:w-4 h-full text-center px-6 py-6 flex flex-column justify-content-between">
          <img src={`/layout/images/logo-dark.svg`} className="h-4rem mt-4" alt="diamond-layout" />

          <form onSubmit={handleSubmit} className="flex flex-column align-items-center gap-4">
            <div className="mb-3">
              <h2>Login to your account</h2>
              <p>
                Forgot password? <a className="text-primary hover:underline cursor-pointer font-medium">Click here</a> to reset.
              </p>
            </div>

            {/* OAuth Sign-In Buttons */}
            <OAuthSignInButton label="Sign in with Google" provider="google" icon="pi pi-google" />
            <OAuthSignInButton label="Sign in with GitHub" provider="github" icon="pi pi-github" />

            {/* Or Login with Credentials */}
            <div className="flex items-center gap-2">
              <hr className="flex-grow border-gray-300" />
              <span className="text-gray-500">or</span>
              <hr className="flex-grow border-gray-300" />
            </div>

            {/* Username and Password Inputs */}
            <InputText
              id="username"
              placeholder="Username"
              className="w-20rem"
              onChange={(e) => setUsername(e.target.value)}
              value={username}
            />
            <InputText
              id="password"
              type="password"
              placeholder="Password"
              className="w-20rem"
              onChange={(e) => setPassword(e.target.value)}
              value={password}
            />
            <Button label="Sign In" className="w-20rem" type="submit" />
          </form>

          <p className="text-color-secondary font-semibold">
            A problem? <a className="text-primary hover:underline cursor-pointer font-medium">Click here</a> and let us help you.
          </p>
        </div>

        <div
          className="w-8 hidden lg:flex flex-column justify-content-between align-items-center px-6 py-6 bg-cover bg-norepeat"
          style={{ backgroundImage: "url('/demo/images/auth/bg-login.jpg')" }}
        >
          <div className="mt-auto mb-auto">
            <span className="block text-white text-7xl font-semibold">
              Access to your <br />
              APART <br />
              Account
            </span>
            <span className="block text-white text-3xl mt-4">
              Lorem ipsum dolor sit amet, consectetur
              <br /> adipiscing elit. Donec posuere velit nec enim
              <br /> sodales, nec placerat erat tincidunt.{' '}
            </span>
          </div>
          <div className="flex align-items-center gap-5">
            <span className="text-white font-semibold">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</span>
            <i className="pi pi-github text-3xl p-1 surface-overlay border-circle cursor-pointer"></i>
            <i className="pi pi-twitter text-3xl p-1 surface-overlay border-circle cursor-pointer"></i>
          </div>
        </div>
      </div>
    </>
  );
};

export default Login;
