import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import os from 'os';
import type { Demo } from '../../../../types/types';

// Helper to resolve path
const resolvePath = (filePath: string) => {
    if (filePath?.startsWith('~/')) {
        return path.join(os.homedir(), filePath.slice(2));
    }
    return filePath || '';
};

const API_BASE_PATH = process.env.NEXT_PUBLIC_BASE_PATH;

// GET handler for fetching transactions
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams;
        const customersParam = searchParams.get('customers');
        const customerIds = customersParam ? customersParam.split(',') : [];

        const resolvedPath = resolvePath(API_BASE_PATH);
        console.log(`Resolved path: ${resolvedPath}`);

        const filePath = path.join(resolvedPath, 'transactions.json');
        console.log(`Looking for transactions at: ${filePath}`);

        if (!fs.existsSync(filePath)) {
            console.error(`File not found: ${filePath}`);
            return NextResponse.json({ message: `Transactions file not found at ${filePath}` }, { status: 404 });
        }

        const data = fs.readFileSync(filePath, 'utf8');
        console.log(`Read ${data.length} bytes from file`);

        let _transactions: Demo.Transaction[] = [];
        try {
            _transactions = JSON.parse(data) as Demo.Transaction[];
            console.log(`Parsed ${_transactions.length} transactions`);
        } catch (parseError) {
            console.error('Error parsing JSON:', parseError);
            return NextResponse.json({ message: `Error parsing transactions JSON: ${parseError.message}` }, { status: 500 });
        }

        const filteredTransactions =
            customerIds.length > 0
                ? _transactions.filter((t) => {
                      const custId = typeof t.customer === 'string' ? t.customer : t.customer?.uuid;
                      return customerIds.includes(custId);
                  })
                : _transactions;

        console.log(`Returning ${filteredTransactions.length} transactions`);
        return NextResponse.json(filteredTransactions);
    } catch (error) {
        console.error('Transaction API error:', error);
        return NextResponse.json({ message: error.message, stack: error.stack }, { status: 500 });
    }
}

// POST handler for adding transactions
export async function POST(request: NextRequest) {
    try {
        const _transaction = await request.json();
        console.log('Received transaction to add:', _transaction);

        const resolvedPath = resolvePath(API_BASE_PATH);
        console.log(`Resolved path for adding transaction: ${resolvedPath}`);

        const filePath = path.join(resolvedPath, 'transactions.json');
        console.log(`Looking for transactions file at: ${filePath}`);

        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            console.log(`Creating directory: ${dir}`);
            fs.mkdirSync(dir, { recursive: true });
        }

        if (!fs.existsSync(filePath)) {
            console.log(`File not found, creating new file: ${filePath}`);
            fs.writeFileSync(filePath, JSON.stringify([], null, 2), 'utf8');
        }

        const data = fs.readFileSync(filePath, 'utf8');
        console.log(`Read ${data.length} bytes from file`);

        let transactions = [];
        try {
            transactions = JSON.parse(data) as Demo.Transaction[];
            console.log(`Parsed ${transactions.length} existing transactions`);
        } catch (parseError) {
            console.error('Error parsing JSON, starting with empty array:', parseError);
        }

        transactions.push(_transaction);
        console.log(`Added transaction, now have ${transactions.length} transactions`);

        fs.writeFileSync(filePath, JSON.stringify(transactions, null, 2), 'utf8');
        console.log(`Wrote ${transactions.length} transactions back to file`);

        return NextResponse.json(_transaction, { status: 201 });
    } catch (error) {
        console.error('Transaction API error:', error);
        return NextResponse.json({ message: error.message, stack: error.stack }, { status: 500 });
    }
}
