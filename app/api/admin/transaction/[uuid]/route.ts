import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import os from 'os';
import type { Demo } from '../../../../../types/types';

// Helper to resolve path
const resolvePath = (filePath: string) => {
    if (filePath?.startsWith('~/')) {
        return path.join(os.homedir(), filePath.slice(2));
    }
    return filePath || '';
};

// Helper to ensure file exists
const ensureFileExists = (filePath: string) => {
    if (!fs.existsSync(filePath)) {
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        fs.writeFileSync(filePath, JSON.stringify([], null, 2), 'utf8');
    }
};

const API_BASE_PATH = process.env.NEXT_PUBLIC_API_BASE_URL || '/demo/data';

export async function PUT(request: NextRequest, { params }: { params: { uuid: string } }) {
    const uuid = params.uuid;

    if (API_BASE_PATH.startsWith('~') || API_BASE_PATH.startsWith('/')) {
        try {
            const updatedTransaction = await request.json();

            const transactionToSave = {
                ...updatedTransaction,
                customer: typeof updatedTransaction.customer === 'string' ? updatedTransaction.customer : updatedTransaction.customer.uuid,
                coordinator: typeof updatedTransaction.coordinator === 'string' ? updatedTransaction.coordinator : updatedTransaction.coordinator.uuid
            };

            const resolvedPath = resolvePath(API_BASE_PATH);
            const filePath = path.join(resolvedPath, 'transactions.json');

            ensureFileExists(filePath);

            const data = fs.readFileSync(filePath, 'utf8');
            const transactions = JSON.parse(data) as Demo.Transaction[];

            const index = transactions.findIndex((t) => t.uuid === uuid);
            if (index === -1) {
                return NextResponse.json({ message: `Transaction with UUID ${uuid} not found` }, { status: 404 });
            }

            transactions[index] = transactionToSave;

            fs.writeFileSync(filePath, JSON.stringify(transactions, null, 2), 'utf8');

            return NextResponse.json(transactionToSave);
        } catch (error) {
            console.error('Transaction API error:', error);
            return NextResponse.json({ message: error.message }, { status: 500 });
        }
    } else {
        try {
            const updatedTransaction = await request.json();
            return NextResponse.json(updatedTransaction);
        } catch (error) {
            return NextResponse.json({ message: error.message }, { status: 500 });
        }
    }
}

export async function DELETE(request: NextRequest, { params }: { params: { uuid: string } }) {
    const uuid = params.uuid;

    if (API_BASE_PATH.startsWith('~') || API_BASE_PATH.startsWith('/')) {
        try {
            const resolvedPath = resolvePath(API_BASE_PATH);
            const filePath = path.join(resolvedPath, 'transactions.json');

            const data = fs.readFileSync(filePath, 'utf8');
            const transactions = JSON.parse(data) as Demo.Transaction[];

            const filteredTransactions = transactions.filter((t) => t.uuid !== uuid);

            if (filteredTransactions.length === transactions.length) {
                return NextResponse.json({ message: `Transaction with UUID ${uuid} not found` }, { status: 404 });
            }

            fs.writeFileSync(filePath, JSON.stringify(filteredTransactions, null, 2), 'utf8');

            return NextResponse.json({ uuid });
        } catch (error) {
            console.error('Transaction API error:', error);
            return NextResponse.json({ message: error.message }, { status: 500 });
        }
    } else {
        return NextResponse.json({ uuid });
    }
}
