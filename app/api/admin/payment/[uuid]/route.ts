import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import type { Demo } from '../../../../../types/types';
import { resolvePath, ensureFileExists } from '../../../../../assets/functions';

const API_BASE_PATH = process.env.NEXT_PUBLIC_BASE_PATH;

export async function PUT(request: NextRequest, { params }: { params: { uuid: string } }) {
    const uuid = params.uuid;

    if (API_BASE_PATH.startsWith('~') || API_BASE_PATH.startsWith('/')) {
        try {
            const _updatedPayment = await request.json();

            const resolvedPath = resolvePath(API_BASE_PATH);
            const filePath = path.join(resolvedPath, 'payments.json');

            ensureFileExists(filePath);

            const data = fs.readFileSync(filePath, 'utf8');
            const payments = JSON.parse(data) as Demo.Payment[];

            const index = payments.findIndex((p) => p.uuid === uuid);
            if (index === -1) {
                return NextResponse.json({ message: `Payment with UUID ${uuid} not found` }, { status: 404 });
            }

            payments[index] = _updatedPayment;

            fs.writeFileSync(filePath, JSON.stringify(payments, null, 2), 'utf8');

            return NextResponse.json(_updatedPayment);
        } catch (error) {
            console.error('Payment API error:', error);
            return NextResponse.json({ message: error.message }, { status: 500 });
        }
    } else {
        try {
            const updatedPayment = await request.json();
            return NextResponse.json(updatedPayment);
        } catch (error) {
            return NextResponse.json({ message: error.message }, { status: 500 });
        }
    }
}

export async function DELETE(request: NextRequest, { params }: { params: { uuid: string } }) {
    const uuid = params.uuid;

    if (API_BASE_PATH.startsWith('~') || API_BASE_PATH.startsWith('/')) {
        try {
            const resolvedPath = resolvePath(API_BASE_PATH);
            const filePath = path.join(resolvedPath, 'payments.json');

            const data = fs.readFileSync(filePath, 'utf8');
            const payments = JSON.parse(data) as Demo.Payment[];

            const filteredPayments = payments.filter((p) => p.uuid !== uuid);

            if (filteredPayments.length === payments.length) {
                return NextResponse.json({ message: `Payment with UUID ${uuid} not found` }, { status: 404 });
            }

            fs.writeFileSync(filePath, JSON.stringify(filteredPayments, null, 2), 'utf8');

            return NextResponse.json({ uuid });
        } catch (error) {
            console.error('Payment API error:', error);
            return NextResponse.json({ message: error.message }, { status: 500 });
        }
    } else {
        return NextResponse.json({ uuid });
    }
}