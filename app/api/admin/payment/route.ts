import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import type { Demo } from '../../../../types/types';
import { resolvePath, ensureFileExists } from '../../../../assets/functions';

const API_BASE_PATH = process.env.NEXT_PUBLIC_BASE_PATH;

// GET handler for fetching payments
export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams;
        const transactionParam = searchParams.get('transaction');
        const transactionIds = transactionParam ? transactionParam.split(',') : [];

        const resolvedPath = resolvePath(API_BASE_PATH);
        console.log(`Resolved path: ${resolvedPath}`);

        const filePath = path.join(resolvedPath, 'payments.json');
        console.log(`Looking for payments at: ${filePath}`);

        if (!fs.existsSync(filePath)) {
            console.error(`File not found: ${filePath}`);
            return NextResponse.json({ message: `Payments file not found at ${filePath}` }, { status: 404 });
        }

        const data = fs.readFileSync(filePath, 'utf8');
        console.log(`Read ${data.length} bytes from file`);

        let _payments: Demo.Payment[] = [];
        try {
            _payments = JSON.parse(data) as Demo.Payment[];
            console.log(`Parsed ${_payments.length} payments`);
        } catch (parseError) {
            console.error('Error parsing JSON:', parseError);
            return NextResponse.json({ message: `Error parsing payments JSON: ${parseError.message}` }, { status: 500 });
        }

        const filteredPayments =
            transactionIds.length > 0
                ? _payments.filter((p) => {
                      const transId = typeof p.transaction === 'string' ? p.transaction : p.transaction?.uuid;
                      return transactionIds.includes(transId);
                  })
                : _payments;

        console.log(`Returning ${filteredPayments.length} payments`);
        return NextResponse.json(filteredPayments);
    } catch (error) {
        console.error('Payment API error:', error);
        return NextResponse.json({ message: error.message, stack: error.stack }, { status: 500 });
    }
}

// POST handler for adding payments
export async function POST(request: NextRequest) {
    try {
        const _payment = await request.json();
        console.log('Received payment to add:', _payment);

        const resolvedPath = resolvePath(API_BASE_PATH);
        console.log(`Resolved path for adding payment: ${resolvedPath}`);

        const filePath = path.join(resolvedPath, 'payments.json');
        console.log(`Looking for payments file at: ${filePath}`);

        ensureFileExists(filePath);

        const data = fs.readFileSync(filePath, 'utf8');
        console.log(`Read ${data.length} bytes from file`);

        let payments = [];
        try {
            payments = JSON.parse(data) as Demo.Payment[];
            console.log(`Parsed ${payments.length} existing payments`);
        } catch (parseError) {
            console.error('Error parsing JSON, starting with empty array:', parseError);
        }

        payments.push(_payment);
        console.log(`Added payment, now have ${payments.length} payments`);

        fs.writeFileSync(filePath, JSON.stringify(payments, null, 2), 'utf8');
        console.log(`Wrote ${payments.length} payments back to file`);

        return NextResponse.json(_payment, { status: 201 });
    } catch (error) {
        console.error('Payment API error:', error);
        return NextResponse.json({ message: error.message, stack: error.stack }, { status: 500 });
    }
}