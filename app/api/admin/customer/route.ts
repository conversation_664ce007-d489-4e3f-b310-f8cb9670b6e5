import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import os from 'os';
import type { Demo } from '../../../../types/types';

// Helper to resolve path
const resolvePath = (filePath: string) => {
    if (filePath?.startsWith('~/')) {
        return path.join(os.homedir(), filePath.slice(2));
    }
    return filePath || '';
};

const API_BASE_PATH = process.env.NEXT_PUBLIC_BASE_PATH;

// GET handler for fetching customers
export async function GET(request: NextRequest) {
    try {
        const resolvedPath = resolvePath(API_BASE_PATH);
        console.log(`Resolved path: ${resolvedPath}`);

        const filePath = path.join(resolvedPath, 'customers.json');
        console.log(`Looking for customers at: ${filePath}`);

        if (!fs.existsSync(filePath)) {
            console.error(`File not found: ${filePath}`);
            return NextResponse.json({ message: `Customers file not found at ${filePath}` }, { status: 404 });
        }

        const data = fs.readFileSync(filePath, 'utf8');
        console.log(`Read ${data.length} bytes from file`);

        let _customers: Demo.Customer[] = [];
        try {
            _customers = JSON.parse(data) as Demo.Customer[];
            console.log(`Parsed ${_customers.length} customers`);
        } catch (parseError) {
            console.error('Error parsing JSON:', parseError);
            return NextResponse.json({ message: `Error parsing customers JSON: ${parseError.message}` }, { status: 500 });
        }
        return NextResponse.json(_customers);
    } catch (error) {
        console.error('Customer API error:', error);
        return NextResponse.json({ message: error.message, stack: error.stack }, { status: 500 });
    }
}

// POST handler for adding customers
export async function POST(request: NextRequest) {
    try {
        const _customer = await request.json();
        console.log('Received customer to add:', _customer);

        const resolvedPath = resolvePath(API_BASE_PATH);
        console.log(`Resolved path for adding customer: ${resolvedPath}`);

        const filePath = path.join(resolvedPath, 'customers.json');
        console.log(`Looking for customers file at: ${filePath}`);

        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            console.log(`Creating directory: ${dir}`);
            fs.mkdirSync(dir, { recursive: true });
        }

        if (!fs.existsSync(filePath)) {
            console.log(`File not found, creating new file: ${filePath}`);
            fs.writeFileSync(filePath, JSON.stringify([], null, 2), 'utf8');
        }

        const data = fs.readFileSync(filePath, 'utf8');
        console.log(`Read ${data.length} bytes from file`);

        let customers = [];
        try {
            customers = JSON.parse(data) as Demo.Customer[];
            console.log(`Parsed ${customers.length} existing customers`);
        } catch (parseError) {
            console.error('Error parsing JSON, starting with empty array:', parseError);
        }

        customers.push(_customer);
        console.log(`Added customer, now have ${customers.length} customers`);

        fs.writeFileSync(filePath, JSON.stringify(customers, null, 2), 'utf8');
        console.log(`Wrote ${customers.length} customers back to file`);

        return NextResponse.json(_customer, { status: 201 });
    } catch (error) {
        console.error('Customer API error:', error);
        return NextResponse.json({ message: error.message, stack: error.stack }, { status: 500 });
    }
}
