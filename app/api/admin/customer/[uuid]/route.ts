import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import type { Demo } from '../../../../../types/types';
import { resolvePath, ensureFileExists } from '../../../../../assets/functions';

const API_BASE_PATH = process.env.NEXT_PUBLIC_API_BASE_URL || '/demo/data';

export async function PUT(request: NextRequest, { params }: { params: { uuid: string } }) {
    const uuid = params.uuid;

    if (API_BASE_PATH.startsWith('~') || API_BASE_PATH.startsWith('/')) {
        try {
            const updatedCustomer = await request.json();

            const customerToSave = {
                ...updatedCustomer,
                coordinator: typeof updatedCustomer.coordinator === 'string' 
                    ? updatedCustomer.coordinator 
                    : updatedCustomer.coordinator?.uuid
            };

            const resolvedPath = resolvePath(API_BASE_PATH);
            const filePath = path.join(resolvedPath, 'customers.json');

            ensureFileExists(filePath);

            const data = fs.readFileSync(filePath, 'utf8');
            const customers = JSON.parse(data) as Demo.Customer[];

            const index = customers.findIndex((c) => c.uuid === uuid);
            if (index === -1) {
                return NextResponse.json({ message: `Customer with UUID ${uuid} not found` }, { status: 404 });
            }

            customers[index] = customerToSave;

            fs.writeFileSync(filePath, JSON.stringify(customers, null, 2), 'utf8');

            return NextResponse.json(customerToSave);
        } catch (error) {
            console.error('Customer API error:', error);
            return NextResponse.json({ message: error.message }, { status: 500 });
        }
    } else {
        try {
            const updatedCustomer = await request.json();
            return NextResponse.json(updatedCustomer);
        } catch (error) {
            return NextResponse.json({ message: error.message }, { status: 500 });
        }
    }
}

export async function DELETE(request: NextRequest, { params }: { params: { uuid: string } }) {
    const uuid = params.uuid;

    if (API_BASE_PATH.startsWith('~') || API_BASE_PATH.startsWith('/')) {
        try {
            const resolvedPath = resolvePath(API_BASE_PATH);
            const filePath = path.join(resolvedPath, 'customers.json');

            const data = fs.readFileSync(filePath, 'utf8');
            const customers = JSON.parse(data) as Demo.Customer[];

            const filteredCustomers = customers.filter((c) => c.uuid !== uuid);

            if (filteredCustomers.length === customers.length) {
                return NextResponse.json({ message: `Customer with UUID ${uuid} not found` }, { status: 404 });
            }

            fs.writeFileSync(filePath, JSON.stringify(filteredCustomers, null, 2), 'utf8');

            return NextResponse.json({ uuid });
        } catch (error) {
            console.error('Customer API error:', error);
            return NextResponse.json({ message: error.message }, { status: 500 });
        }
    } else {
        return NextResponse.json({ uuid });
    }
}