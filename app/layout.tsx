'use client';

import { Provider } from 'react-redux';
import { store } from './state/store';
import { LayoutProvider } from '../context/LayoutProvider';
import { PrimeReactProvider } from 'primereact/api';
import 'primereact/resources/primereact.css';
import 'primeflex/primeflex.css';
import 'primeicons/primeicons.css';
import '../styles/layout/layout.scss';
import '../styles/demo/Demos.scss';
import AuthProvider from '../context/AuthProvider';
import DashboardProvider from '../context/use-dashboard-provider';

export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <Provider store={store}>
            <html lang="en" suppressHydrationWarning>
                <head>
                    <link id="theme-link" href={`/theme/theme-light/blue/theme.css`} rel="stylesheet"></link>
                </head>
                <body>
                    <PrimeReactProvider>
                        <AuthProvider>
                            <DashboardProvider>
                                <LayoutProvider>{children}</LayoutProvider>
                            </DashboardProvider>
                        </AuthProvider>
                    </PrimeReactProvider>
                </body>
            </html>
        </Provider>
    );
}
