import { configureStore } from '@reduxjs/toolkit';
import authReducer from '../../services/authService';
import CustomerService from '../../services/customerService';
import PaymentService from '../../services/paymentService';
import TransactionService from '../../services/transactionService';

export const store = configureStore({
    reducer: {
        auth: authReducer,
        transaction: TransactionService,
        payment: PaymentService,
        customer: CustomerService,
    }
});

export type AppDispatch = typeof store.dispatch;