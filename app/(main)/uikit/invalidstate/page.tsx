'use client';

import React, { useState, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { InputMask } from 'primereact/inputmask';
import { InputNumber } from 'primereact/inputnumber';
import { AutoComplete, AutoCompleteCompleteEvent } from 'primereact/autocomplete';
import { Calendar, CalendarChangeEvent } from 'primereact/calendar';
import { Chips } from 'primereact/chips';
import { Dropdown } from 'primereact/dropdown';
import { MultiSelect } from 'primereact/multiselect';
import { Password } from 'primereact/password';
import { CountryService } from '../../../../services/countryService';
import type { Demo } from '../../../../types/types';

const InvalidStateDemo = () => {
    const [countries, setCountries] = useState<Demo.Country[]>([]);
    const [filteredCountries, setFilteredCountries] = useState<Demo.Country[]>([]);
    const [value1, setValue1] = useState('');
    const [value2, setValue2] = useState(null);
    const [value3, setValue3] = useState<Date | Date[] | string | null>(null);
    const [value4, setValue4] = useState<any[]>([]);
    const [value5, setValue5] = useState('');
    const [value6, setValue6] = useState('');
    const [value7, setValue7] = useState(0);
    const [value8, setValue8] = useState(null);
    const [value9, setValue9] = useState(null);
    const [value10, setValue10] = useState('');

    const cities = [
        { name: 'New York', code: 'NY' },
        { name: 'Rome', code: 'RM' },
        { name: 'London', code: 'LDN' },
        { name: 'Istanbul', code: 'IST' },
        { name: 'Paris', code: 'PRS' }
    ];

    useEffect(() => {
        CountryService.getCountries().then((countries) => {
            setCountries(countries);
        });
    }, []);

    const searchCountry = (event: AutoCompleteCompleteEvent) => {
        // in a real application, make a request to a remote url with the query and
        // return filtered results, for demo we filter at client side
        const filtered = [];
        const query = event.query;
        for (let i = 0; i < countries.length; i++) {
            const country = countries[i];
            if (country.name.toLowerCase().indexOf(query.toLowerCase()) === 0) {
                filtered.push(country);
            }
        }
        setFilteredCountries(filtered);
    };

    const onCalendarChange = (e: CalendarChangeEvent) => {
        setValue3(e.value!);
    };

    return (
        <div className="card">
            <h5>Invalid State</h5>
            <div className="grid p-fluid">
                <div className="col-12 md:col-6">
                    <div className="field mt-3">
                        <label htmlFor="inputtext">InputText</label>
                        <InputText type="text" id="inputtext" value={value1} onChange={(e) => setValue1(e.target.value)} className="p-invalid" />
                    </div>
                    <div className="field">
                        <label htmlFor="autocomplete">AutoComplete</label>
                        <AutoComplete id="autocomplete" value={value2} onChange={(e) => setValue2(e.value)} suggestions={filteredCountries} completeMethod={searchCountry} field="name" className="p-invalid" />
                    </div>
                    <div className="field">
                        <label htmlFor="calendar">Calendar</label>
                        <Calendar inputId="calendar" value={value3} onChange={onCalendarChange} className="p-invalid" showIcon />
                    </div>
                    <div className="field">
                        <label htmlFor="chips">Chips</label>
                        <Chips inputId="chips" value={value4} onChange={(e) => setValue4(e.value ?? [])} className="p-invalid" />
                    </div>
                    <div className="field">
                        <label htmlFor="password">Password</label>
                        <Password inputId="password" value={value5} onChange={(e) => setValue5(e.target.value)} className="p-invalid" />
                    </div>
                </div>

                <div className="col-12 md:col-6">
                    <div className="field mt-3">
                        <label htmlFor="inputmask">InputMask</label>
                        <InputMask id="inputmask" mask="99/99/9999" value={value6} onChange={(e) => setValue6(e.value ?? '')} className="p-invalid" />
                    </div>
                    <div className="field">
                        <label htmlFor="inputnumber">InputNumber</label>
                        <InputNumber id="inputnumber" value={value7} onValueChange={(e) => setValue7(e.target.value ?? 0)} className="p-invalid" />
                    </div>
                    <div className="field">
                        <label htmlFor="dropdown">Dropdown</label>
                        <Dropdown id="dropdown" options={cities} value={value8} onChange={(e) => setValue8(e.value)} optionLabel="name" className="p-invalid" />
                    </div>
                    <div className="field">
                        <label htmlFor="multiselect">MultiSelect</label>
                        <MultiSelect id="multiselect" options={cities} value={value9} onChange={(e) => setValue9(e.value)} optionLabel="name" className="p-invalid" />
                    </div>
                    <div className="field">
                        <label htmlFor="textarea">Textarea</label>
                        <InputTextarea id="textarea" rows={3} cols={30} value={value10} onChange={(e) => setValue10(e.target.value)} className="p-invalid" />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default InvalidStateDemo;
