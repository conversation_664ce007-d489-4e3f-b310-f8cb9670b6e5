import { Metadata } from 'next';
import DashboardProvider from '../../context/use-dashboard-provider';
import AppLayout from '../../layout/AppLayout';

interface MainLayoutProps {
    children: React.ReactNode;
}

export const metadata: Metadata = {
    title: 'Smart Solutions - Aspire',
    description: 'The ultimate collection of design-agnostic, flexible and accessible React UI Components.',
    robots: { index: false, follow: false },
    viewport: { initialScale: 1, width: 'device-width' },
    openGraph: {
        type: 'website',
        title: 'PrimeReact Diamond-REACT',
        url: 'https://www.primefaces.org/diamond-react',
        description: 'The ultimate collection of design-agnostic, flexible and accessible React UI Components.',
        images: ['https://www.primefaces.org/static/social/diamond-react.png'],
        ttl: 604800
    },
    icons: {
        icon: '/favicon.ico'
    }
};

export default function MainLayout({ children }: MainLayoutProps) {
    return (
        <AppLayout>
            <DashboardProvider>{children}</DashboardProvider>
        </AppLayout>
    );
}
