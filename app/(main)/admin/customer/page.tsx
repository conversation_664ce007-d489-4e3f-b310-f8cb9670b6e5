'use client';
import { Filter<PERSON>atchMode, FilterOperator } from 'primereact/api';
import { AutoComplete, AutoCompleteCompleteEvent } from 'primereact/autocomplete';
import { Calendar } from 'primereact/calendar';
import { Column } from 'primereact/column';
import { DataTable, DataTableFilterMeta } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { Dropdown } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Toolbar } from 'primereact/toolbar';
import { classNames } from 'primereact/utils';
import React, { useEffect, useRef, useState } from 'react';
import { countries, loggedUser } from '../../../../assets/constants';
import { InputAmount, InputPercentage } from '../../../../components/inputs';
import { useDashboardContext } from '../../../../context/use-dashboard-context';
import { useCustomers } from '../../../../hooks/useCustomers';
import type { Demo } from '../../../../types/types';
import {
    activityBodyTemplate,
    coordinatorBodyTemplate,
    coordinatorBodyTemplate_,
    countryBodyTemplate,
    dateBodyTemplate,
    dialogFooter,
    headerTemplate,
    leftToolbarTemplate,
    mobileBodyTemplate,
    rightToolbarTemplate,
    statusBodyTemplate,
    whatsAppBodyTemplate
} from '../../utilities/grids/grid';
import { Button } from 'primereact/button';

// Initial state constants
const initCustomer: Demo.Customer = {
    uuid: null,
    username: null,
    firstname: null,
    lastname: null,
    birthday: null,
    contact: null,
    address: {
        street: null,
        city: null,
        zip: null,
        country: {
            code: 'ga',
            name: 'Gabon',
            areaCode: '+241',
            currency: 'XAF'
        }
    },
    status: 'ACTIVE',
    balance: 500000,
    roles: ['CUSTOMER'],
    activity: 0,
    rating: 5,
    image: null,
    coordinator: null
};

const Customer = () => {
    // ===== References =====
    const dt = useRef(null);

    // ===== Context hooks =====
    const { toast, customers, setCustomers, setUuidToCustomer,loading: contextLoading, sortField, setSortField, sortOrder, setSortOrder } = useDashboardContext();

    // ===== Custom hooks =====
    const { loading: customerLoading, addCustomer, updateCustomer } = useCustomers(toast);

    // Combined loading state
    const loading = contextLoading || customerLoading;

    // ===== Table state =====
    const [filters, setFilters] = useState<DataTableFilterMeta>({});
    const [globalFilterValue, setGlobalFilterValue] = useState('');

    // ===== Coordinator selection state =====
    const [autoCoordinator, setAutoCoordinator] = useState<Demo.Customer[]>([]);
    const [autoFilteredCoordinator, setAutoFilteredCoordinator] = useState<Demo.Customer[]>([]);

    // ===== Customer form state =====
    const [selectedCustomer, setSelectedCustomer] = useState<Demo.Customer | null>(null);
    const [customerDialog, setSelectedCustomerDialog] = useState(false);
    const [submitted, setSubmitted] = useState(false);
    const [ageValid, setAgeValid] = useState(true);

    // ===== Initialization functions =====
    const initFilters = () => {
        setFilters({
            global: { value: null, matchMode: FilterMatchMode.CONTAINS },
            balance: { value: null, matchMode: FilterMatchMode.EQUALS },
            insertDate: {
                operator: FilterOperator.AND,
                constraints: [{ value: null, matchMode: FilterMatchMode.DATE_IS }]
            },
            rating: { value: null, matchMode: FilterMatchMode.GREATER_THAN }
        });
        setGlobalFilterValue('');
    };

    const isAtLeast18 = (date: Date) => {
        const today = new Date();
        let age = today.getFullYear() - date.getFullYear();
        const m = today.getMonth() - date.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < date.getDate())) {
            age--;
        }
        return age >= 18;
    };

    // Initialize coordinator list when customers change
    useEffect(() => {
        if (customers.length > 0) {
            setAutoCoordinator(customers.filter((_coordinator) => _coordinator.status === 'ACTIVE' && _coordinator.roles.includes('COORDINATOR')));
        }
    }, [customers]);

    // ===== Table event handlers =====
    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        let _filters = { ...filters };

        // Ensure global filter exists before setting value
        if (!_filters['global']) {
            _filters['global'] = { value: null, matchMode: FilterMatchMode.CONTAINS };
        }

        (_filters['global'] as any).value = value;
        setFilters(_filters);
        setGlobalFilterValue(value);
    };

    const clearFilter = () => {
        initFilters();
    };

    const exportCSV = () => {
        dt.current.exportCSV();
    };

    // ===== Dialog event handlers =====
    const openNew = () => {
        // Create a fresh copy of initCustomer with Gabon as default country
        const _customer: Demo.Customer = {
            ...initCustomer,
            contact: {
                phone: null,
                email: null,
                whatsApp: null,
                mobileCash: null
            },
            address: {
                street: null,
                city: null,
                zip: null,
                country: {
                    code: 'ga',
                    name: 'Gabon',
                    areaCode: '+241',
                    currency: 'XAF'
                }
            },
            status: 'ACTIVE',
            balance: 500000,
            roles: ['CUSTOMER'],
            activity: 0,
            rating: 5,
            image: null,
            coordinator: null,
            insertDate: new Date()
        };

        setSelectedCustomer(_customer);
        setSubmitted(false);
        setSelectedCustomerDialog(true);
    };

    const hideDialog = () => {
        setSubmitted(false);
        setSelectedCustomerDialog(false);
    };

    // ===== Form event handlers =====
    const searchCoordinator = (event: AutoCompleteCompleteEvent) => {
        setTimeout(() => {
            const query = event.query.trim().toLowerCase();
            if (!query.length) {
                setAutoFilteredCoordinator([...autoCoordinator]);
            } else {
                setAutoFilteredCoordinator(
                    autoCoordinator.filter((coordinator) => {
                        return coordinator.firstname.toLowerCase().includes(query) || (coordinator.lastname && coordinator.lastname.toLowerCase().includes(query));
                    })
                );
            }
        }, 250);
    };

    // Update the saveCustomer function with validation
    const saveCustomer = async () => {
        setSubmitted(true);

        try {
            if (selectedCustomer?.uuid) {
                await updateCustomer(selectedCustomer, setCustomers, setUuidToCustomer);
            } else {
                await addCustomer(selectedCustomer, setCustomers);
            }

            setSelectedCustomerDialog(false);
            setSelectedCustomer(initCustomer);
            setSubmitted(false);
        } catch (err) {
            console.error('Failed to save customer:', err);
            toast.current.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to save customer',
                life: 3000
            });
        }
    };

    // ===== Render UI =====
    // Show loading spinner while data is being loaded
    if (loading && customers.length === 0) {
        return (
            <div className="flex justify-content-center align-items-center" style={{ height: '80vh' }}>
                <ProgressSpinner style={{ width: '50px', height: '50px' }} strokeWidth="4" />
            </div>
        );
    }

    const customerDialogFooter = (
        <>
            <Button label="Cancel" icon="pi pi-times" className="p-button-text" onClick={hideDialog} />
            <Button
                label="Save"
                icon="pi pi-check"
                className="p-button-text"
                onClick={saveCustomer}
                disabled={!selectedCustomer?.firstname || !selectedCustomer?.lastname || !selectedCustomer?.birthday || !ageValid || !selectedCustomer?.coordinator || !selectedCustomer?.contact?.whatsApp || !selectedCustomer?.contact?.mobileCash}
            />
        </>
    );

    return (
        <div className="container">
            <div className="card">
                <Toolbar className="mb-4" start={leftToolbarTemplate(openNew)} end={rightToolbarTemplate(exportCSV)} />
                <DataTable
                    ref={dt}
                    value={customers}
                    header={headerTemplate('Customers', onGlobalFilterChange)}
                    paginator
                    rows={50}
                    responsiveLayout="scroll"
                    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                    rowsPerPageOptions={[25, 50, 100]}
                    filters={filters}
                    loading={loading}
                    sortField={sortField}
                    sortOrder={sortOrder}
                    onSort={(e) => {
                        setSortField(e.sortField);
                        setSortOrder(e.sortOrder as 0 | 1 | -1);
                    }}
                >
                    <Column field="insertDate" header="Insert Date" sortable style={{ display: 'none' }} />
                    <Column field="firstname" header="Firstname" sortable headerClassName="white-space-nowrap" style={{ width: '15%' }}></Column>
                    <Column field="lastname" header="Lastname" sortable headerClassName="white-space-nowrap" style={{ width: '15%' }}></Column>
                    <Column field="address.country.name" header="Country" sortable body={(rowData) => countryBodyTemplate(rowData.address.country)} headerClassName="white-space-nowrap" style={{ width: '15%' }}></Column>
                    <Column field="coordinator.firstname" header="Coordinator" body={(rowData) => coordinatorBodyTemplate(rowData.coordinator)} headerClassName="white-space-nowrap" style={{ width: '15%' }} sortable></Column>
                    <Column field="birthday" header="Birthday" sortable body={(rowData) => dateBodyTemplate(rowData.birthday)} headerClassName="white-space-nowrap" style={{ width: '15%' }} />
                    <Column field="contact.mobileCash" header="Mobile Cash" body={(rowData) => mobileBodyTemplate(rowData.contact)} headerClassName="white-space-nowrap" style={{ width: '15%' }} />
                    <Column field="contact.whatsApp" header="WhatsApp" body={(rowData) => whatsAppBodyTemplate(rowData.contact)} headerClassName="white-space-nowrap" style={{ width: '20%' }} />
                    <Column field="status" header="Status" body={(rowData) => statusBodyTemplate(rowData.status)} sortable headerClassName="white-space-nowrap" style={{ width: '10%' }} />
                    <Column field="activity" header="Activity" body={(rowData) => activityBodyTemplate(rowData.activity)} headerClassName="white-space-nowrap" style={{ width: '10%' }} sortable></Column>
                </DataTable>

                <Dialog visible={customerDialog} style={{ width: '90vw', maxWidth: '1400px' }} header="Customer Details" modal className="p-fluid" footer={customerDialogFooter} onHide={hideDialog}>
                    <div className="col-12">
                        <div className="card">
                            <h5>Customer</h5>
                            <div className="grid p-fluid">
                                <div className="col-12 md:col-6">
                                    <div className="p-inputgroup">
                                        <span className="p-inputgroup-addon">
                                            <i className="pi pi-user"></i>
                                        </span>
                                        <InputText
                                            id="firstname"
                                            placeholder="Firstname"
                                            value={selectedCustomer?.firstname ?? ''}
                                            onChange={(e) => {
                                                setSelectedCustomer((prevCustomer) => ({
                                                    ...prevCustomer,
                                                    firstname: e.target.value || null
                                                }));
                                            }}
                                        />
                                    </div>
                                </div>

                                <div className="col-12 md:col-6">
                                    <div className="p-inputgroup">
                                        <span className="p-inputgroup-addon">
                                            <i className="pi pi-user"></i>
                                        </span>
                                        <InputText
                                            id="lastname"
                                            placeholder="Lastname"
                                            value={selectedCustomer?.lastname ?? ''}
                                            onChange={(e) => {
                                                setSelectedCustomer((prevCustomer) => ({
                                                    ...prevCustomer,
                                                    lastname: e.target.value || null
                                                }));
                                            }}
                                        />
                                    </div>
                                </div>

                                <div className="col-12 md:col-6">
                                    <div className="p-inputgroup">
                                        <Calendar
                                            showIcon
                                            showButtonBar
                                            placeholder="Birthday"
                                            value={selectedCustomer?.birthday}
                                            onChange={(e) => {
                                                const date = Array.isArray(e.value) ? null : new Date(e.value);
                                                if (date) {
                                                    setAgeValid(isAtLeast18(date));
                                                } else {
                                                    setAgeValid(true);
                                                }
                                                setSelectedCustomer((prevCustomer) => ({
                                                    ...prevCustomer,
                                                    birthday: date
                                                }));
                                            }}
                                            className={classNames({
                                                'p-invalid': submitted && (!selectedCustomer?.birthday || !ageValid)
                                            })}
                                        />
                                    </div>
                                    {submitted && (!selectedCustomer?.birthday || !ageValid) && <small className="p-invalid">Must be at least 18 years old.</small>}
                                </div>

                                <div className="col-12 md:col-6">
                                    <div className="p-inputgroup">
                                        <span className="p-inputgroup-addon">
                                            <i className="pi pi-globe"></i>
                                        </span>
                                        <Dropdown
                                            placeholder="Select a Country"
                                            value={selectedCustomer?.address.country}
                                            options={countries}
                                            optionLabel="name"
                                            onChange={(e) => {
                                                setSelectedCustomer((prevCustomer) => ({
                                                    ...prevCustomer,
                                                    address: {
                                                        ...prevCustomer.address,
                                                        country: e.value
                                                    }
                                                }));
                                            }}
                                            itemTemplate={countryBodyTemplate}
                                            filter
                                            showClear
                                            filterBy="name,code"
                                        />
                                    </div>
                                </div>

                                <div className="col-12 md:col-6">
                                    <div className="p-inputgroup">
                                        <span className="p-inputgroup-addon">
                                            <i className="pi pi-compass"></i>
                                        </span>
                                        <Dropdown
                                            placeholder="type"
                                            value={selectedCustomer?.status}
                                            options={['ACTIVE', 'INACTIVE', 'PENDING', 'BLOCKED']}
                                            onChange={(e) => {
                                                setSelectedCustomer((prevCustomer) => ({
                                                    ...prevCustomer,
                                                    status: e.value
                                                }));
                                            }}
                                        />
                                    </div>
                                </div>

                                <InputAmount
                                    id="amount"
                                    placeholder="Amount"
                                    value={selectedCustomer?.balance}
                                    disabled={loggedUser.role !== 'ADMIN' && loggedUser.role !== 'COORDINATOR'}
                                    onChange={(e) => {
                                        setSelectedCustomer((prevCustomer: Demo.Customer) => ({
                                            ...prevCustomer,
                                            balance: e.value || null
                                        }));
                                    }}
                                    submitted={submitted}
                                    min={200000}
                                    max={1000000}
                                    step={100000}
                                />
                            </div>
                        </div>

                        <div className="card">
                            <h5>Coordinator</h5>
                            <div className="grid p-fluid">
                                <div className="col-12 md:col-6">
                                    <div className="p-inputgroup">
                                        <span className="p-inputgroup-addon">
                                            <i className="pi pi-user"></i>
                                        </span>

                                        <AutoComplete
                                            id="coordinator"
                                            placeholder="Coordinator Name"
                                            field="firstname"
                                            dropdown
                                            value={selectedCustomer?.coordinator && typeof selectedCustomer?.coordinator !== 'string' ? selectedCustomer?.coordinator.firstname : ''}
                                            onChange={(e) => {
                                                const coordinator = autoCoordinator.find((coord) => coord.uuid === e.value.uuid);
                                                setSelectedCustomer((prevCustomer: Demo.Customer) => ({
                                                    ...prevCustomer,
                                                    coordinator: coordinator || null
                                                }));
                                            }}
                                            suggestions={autoFilteredCoordinator}
                                            completeMethod={searchCoordinator}
                                            itemTemplate={coordinatorBodyTemplate_}
                                            className={classNames({
                                                'p-invalid': submitted && (!selectedCustomer?.coordinator || typeof selectedCustomer?.coordinator === 'string' || !selectedCustomer?.coordinator.firstname)
                                            })}
                                        />
                                    </div>
                                    {submitted && (!selectedCustomer?.coordinator || typeof selectedCustomer?.coordinator === 'string' || !selectedCustomer?.coordinator.firstname) && <small className="p-invalid">Select a coordinator.</small>}
                                </div>

                                <InputPercentage
                                    id="coordinatorInterest"
                                    placeholder="Percentage"
                                    value={selectedCustomer?.coordinator && typeof selectedCustomer?.coordinator !== 'string' ? selectedCustomer?.coordinator.coordinatorInterest : 0}
                                    onChange={(e) => {
                                        setSelectedCustomer((prevCustomer: Demo.Customer) => ({
                                            ...prevCustomer,
                                            coordinator: {
                                                ...(prevCustomer.coordinator as Demo.Customer),
                                                coordinatorInterest: e.value || null
                                            }
                                        }));
                                    }}
                                />
                            </div>
                        </div>

                        <div className="card">
                            <h5>Contact</h5>
                            <div className="grid p-fluid">
                                <div className="col-12 md:col-6">
                                    <div className="p-inputgroup">
                                        <span className="p-inputgroup-addon">
                                            <i className="pi pi-phone"></i>
                                        </span>
                                        <InputText
                                            id="phone"
                                            placeholder="+241 (076) 25 65 62"
                                            value={selectedCustomer?.contact?.phone ?? ''}
                                            onChange={(e) => {
                                                setSelectedCustomer((prevCustomer: Demo.Customer) => ({
                                                    ...prevCustomer,
                                                    contact: {
                                                        ...prevCustomer.contact,
                                                        phone: e.target.value || null
                                                    }
                                                }));
                                            }}
                                        />
                                    </div>
                                </div>

                                <div className="col-12 md:col-6">
                                    <div className="p-inputgroup">
                                        <span className="p-inputgroup-addon">
                                            <i className="pi pi-whatsapp"></i>
                                        </span>
                                        <InputText
                                            id="whatsapp"
                                            placeholder="+241 (076) 25 65 62"
                                            value={selectedCustomer?.contact?.whatsApp ?? ''}
                                            onChange={(e) => {
                                                setSelectedCustomer((prevCustomer: Demo.Customer) => ({
                                                    ...prevCustomer,
                                                    contact: {
                                                        ...prevCustomer.contact,
                                                        whatsApp: e.target.value || null
                                                    }
                                                }));
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className="grid p-fluid">
                                <div className="col-12 md:col-6">
                                    <div className="p-inputgroup">
                                        <span className="p-inputgroup-addon">
                                            <i className="pi pi-mobile"></i>
                                        </span>
                                        <InputText
                                            id="mobileCash"
                                            placeholder="(076) 25 65 62"
                                            value={selectedCustomer?.contact?.mobileCash ?? ''}
                                            onChange={(e) => {
                                                setSelectedCustomer((prevCustomer: Demo.Customer) => ({
                                                    ...prevCustomer,
                                                    contact: {
                                                        ...prevCustomer.contact,
                                                        mobileCash: e.target.value || null
                                                    }
                                                }));
                                            }}
                                        />
                                    </div>
                                </div>

                                <div className="col-12 md:col-6">
                                    <div className="p-inputgroup">
                                        <span className="p-inputgroup-addon">
                                            <i className="pi pi-at"></i>
                                        </span>
                                        <InputText
                                            id="email"
                                            placeholder="<EMAIL>"
                                            value={selectedCustomer?.contact?.email ?? ''}
                                            onChange={(e) => {
                                                setSelectedCustomer((prevCustomer: Demo.Customer) => ({
                                                    ...prevCustomer,
                                                    contact: {
                                                        ...prevCustomer.contact,
                                                        email: e.target.value || null
                                                    }
                                                }));
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="card">
                            <h5>Description</h5>
                            <InputTextarea placeholder="Your Message" rows={3} cols={30} />
                        </div>
                    </div>
                </Dialog>
            </div>
        </div>
    );
};

export default Customer;
