'use client';
import { Filter<PERSON>atchMode, FilterOperator } from 'primereact/api';
import { AutoComplete, AutoCompleteCompleteEvent } from 'primereact/autocomplete';
import { Calendar } from 'primereact/calendar';
import { Column } from 'primereact/column';
import { DataTable, DataTableFilterMeta } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { Dropdown } from 'primereact/dropdown';
import { InputTextarea } from 'primereact/inputtextarea';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Rating } from 'primereact/rating';
import { Toolbar } from 'primereact/toolbar';
import { classNames } from 'primereact/utils';
import React, { useEffect, useRef, useState } from 'react';
import { loggedUser } from '../../../../assets/constants';
import { InputAmount } from '../../../../components/inputs';
import { useDashboardContext } from '../../../../context/use-dashboard-context';
import { usePayments } from '../../../../hooks/usePayments';
import type { Demo } from '../../../../types/types';
import { coordinatorBodyTemplate, currencyBodyTemplate, dateBodyTemplate, dialogFooter, headerTemplate, leftToolbarTemplate, ratingBodyTemplate, rightToolbarTemplate, typeBodyTemplate } from '../../utilities/grids/grid';
import { InputNumber } from 'primereact/inputnumber';

// Initial state constants
const initPayment: Demo.Payment = {
    uuid: null,
    insertDate: new Date(),
    name: null,
    transaction: null,
    amount: 200000,
    installment: 130000,
    type: 'PAYMENT',
    rating: 5,
    description: null
};

const Payment = () => {
    // ===== References =====
    const dt = useRef(null);

    // ===== Context hooks =====
    const { toast, customers, transactions, payments, setPayments, loading: contextLoading, sortField, setSortField, sortOrder, setSortOrder } = useDashboardContext();

    // ===== Custom hooks =====
    const { loading: paymentLoading, addPayment, updatePayment } = usePayments(toast);

    // Combined loading state
    const loading = contextLoading || paymentLoading;

    // ===== Table state =====
    const [filters, setFilters] = useState<DataTableFilterMeta>({});
    const [globalFilterValue, setGlobalFilterValue] = useState('');

    // ===== Transaction selection state =====
    const [autoTransaction, setAutoTransaction] = useState<Demo.Transaction[]>([]);
    const [autoFilteredTransaction, setAutoFilteredTransaction] = useState<Demo.Transaction[]>([]);
    const [selectedTransaction, setSelectedTransaction] = useState<Demo.Transaction | null>(null);

    // ===== Payment form state =====
    const [payment, setPayment] = useState<Demo.Payment>(initPayment);
    const [submitted, setSubmitted] = useState(false);
    const [paymentDialog, setPaymentDialog] = useState(false);

    // ===== Dialog state =====
    const [dialogFilters, setDialogFilters] = useState<DataTableFilterMeta>({});
    const [dialogGlobalFilterValue, setDialogGlobalFilterValue] = useState('');
    const [latestPayments, setLatestPayments] = useState<Demo.Payment[]>([]);

    // ===== Initialization functions =====
    const initFilters = () => {
        setFilters({
            global: { value: null, matchMode: FilterMatchMode.CONTAINS },
            amount: { value: null, matchMode: FilterMatchMode.EQUALS },
            installment: { value: null, matchMode: FilterMatchMode.EQUALS },
            insertDate: {
                operator: FilterOperator.AND,
                constraints: [{ value: null, matchMode: FilterMatchMode.DATE_IS }]
            },
            type: { value: null, matchMode: FilterMatchMode.EQUALS }
        });
        setGlobalFilterValue('');
    };

    // Initialize transaction options
    useEffect(() => {
        if (transactions?.length > 0) {
            setAutoTransaction(transactions.filter((_transaction) => _transaction.status === 'RUNNING'));
        }
        initFilters();
    }, [transactions]);

    // Add this useEffect to handle payment initialization when transaction is selected
    useEffect(() => {
        if (selectedTransaction) {
            // Get transaction details
            const transactionInstallment = selectedTransaction.installment || 0;

            // Initialize payment with values from selected transaction
            setPayment({
                uuid: null,
                insertDate: new Date(),
                name: selectedTransaction.name,
                transaction: selectedTransaction,
                amount: transactionInstallment,
                installment: transactionInstallment,
                type: 'PAYMENT',
                rating: 5,
                description: null
            });
        }
    }, [selectedTransaction]);

    // ===== Table event handlers =====
    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        let _filters = { ...filters };
        (_filters['global'] as any).value = value;
        setFilters(_filters);
        setGlobalFilterValue(value);
    };

    const exportCSV = () => {
        dt.current.exportCSV();
    };

    // ===== Dialog event handlers =====
    const openNew = () => {
        setPayment(initPayment);
        setSelectedTransaction(null);
        setSubmitted(false);
        setPaymentDialog(true);
    };

    const hideDialog = () => {
        setSubmitted(false);
        setPaymentDialog(false);
        setSelectedTransaction(null);
    };

    // ===== Form event handlers =====
    const searchTransaction = (event: AutoCompleteCompleteEvent) => {
        setTimeout(() => {
            const query = event.query.trim().toLowerCase();
            if (!query.length) {
                setAutoFilteredTransaction([...autoTransaction]);
            } else {
                setAutoFilteredTransaction(
                    autoTransaction.filter((transaction) => {
                        if (typeof transaction.customer === 'string') return false;
                        return transaction.customer.firstname.toLowerCase().includes(query) || (transaction.customer.lastname && transaction.customer.lastname.toLowerCase().includes(query));
                    })
                );
            }
        }, 250);
    };

    const savePayment = async () => {
        setSubmitted(true);

        try {
            if (payment.transaction) {
                if (payment.uuid) {
                    await updatePayment(payment);
                } else {
                    await addPayment(payment, setPayments);
                }

                setPaymentDialog(false);
                setPayment(initPayment);
                setSubmitted(false);
            }
        } catch (err) {
            console.error('Failed to save payment:', err);
            toast.current.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to save payment',
                life: 3000
            });
        }
    };

    // ===== Dialog filter change handler =====
    const onDialogGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        let _filters = { ...dialogFilters };
        
        if (!_filters['global']) {
            _filters['global'] = { value: null, matchMode: FilterMatchMode.CONTAINS };
        }

        (_filters['global'] as any).value = value;
        setDialogFilters(_filters);
        setDialogGlobalFilterValue(value);
    };

    // ===== Effect to update latest payments =====
    useEffect(() => {
        if (selectedTransaction && selectedTransaction.uuid) {
            // Filter payments for the selected transaction
            const txPayments = payments
                .filter((payment) => {
                    if (!payment.transaction) return false;
                    if (typeof payment.transaction === 'string') {
                        return payment.transaction === selectedTransaction.uuid;
                    }
                    return payment.transaction.uuid === selectedTransaction.uuid;
                })
                .sort((a, b) => new Date(b.insertDate).getTime() - new Date(a.insertDate).getTime())
                .slice(0, 10);
            setLatestPayments(txPayments);
        } else {
            setLatestPayments([]);
        }
    }, [selectedTransaction, payments]);

    // ===== Render UI =====
    // Show loading spinner while data is being loaded
    if (loading && payments.length === 0) {
        return (
            <div className="flex justify-content-center align-items-center" style={{ height: '80vh' }}>
                <ProgressSpinner style={{ width: '50px', height: '50px' }} strokeWidth="4" />
            </div>
        );
    }

    return (
        <div className="card">
            <Toolbar className="mb-4" left={leftToolbarTemplate(openNew)} right={rightToolbarTemplate(exportCSV)}></Toolbar>

            {/* Payments DataTable */}
            <DataTable
                ref={dt}
                value={payments}
                header={headerTemplate('Payments', onGlobalFilterChange)}
                paginator
                rows={50}
                responsiveLayout="scroll"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                rowsPerPageOptions={[25, 50, 100]}
                filters={filters}
                loading={loading}
                sortField={sortField}
                sortOrder={sortOrder}
                onSort={(e) => {
                    setSortField(e.sortField);
                    setSortOrder(e.sortOrder as 0 | 1 | -1);
                }}
            >
                <Column field="insertDate" header="Insert Date" sortable body={(rowData) => dateBodyTemplate(rowData.insertDate)} headerClassName="white-space-nowrap" style={{ width: '10%' }} />
                <Column field="name" header="Customer" sortable headerClassName="white-space-nowrap" style={{ width: '15%' }} />
                <Column field="amount" header="Amount" body={(rowData) => currencyBodyTemplate(rowData.amount)} sortable headerClassName="white-space-nowrap" style={{ width: '10%' }} />
                <Column field="installment" header="Installment" body={(rowData) => currencyBodyTemplate(rowData.installment)} sortable headerClassName="white-space-nowrap" style={{ width: '10%' }} />
                <Column field="transaction.coordinator.firstname" header="Coordinator" body={(rowData) => coordinatorBodyTemplate(rowData.transaction.coordinator)} headerClassName="white-space-nowrap" style={{ width: '15%' }} sortable></Column>
                <Column field="type" header="Type" body={(rowData) => typeBodyTemplate(rowData.type)} sortable headerClassName="white-space-nowrap" style={{ width: '10%' }} />
                <Column field="rating" header="Rating" body={(rowData) => ratingBodyTemplate(rowData.rating)} sortable style={{ width: '10%' }} />
                <Column field="description" header="Description" headerClassName="white-space-nowrap" style={{ width: '20%' }} />
            </DataTable>

            {/* Payment Dialog */}
            <Dialog visible={paymentDialog} style={{ width: '90vw', maxWidth: '1400px' }} header="Payment Details" modal className="p-fluid" footer={dialogFooter(hideDialog, savePayment)} onHide={hideDialog}>
                <div className="col-12">
                    {/* Transaction Section */}
                    <div className="card">
                        <h5>Transaction</h5>
                        <div className="grid p-fluid">
                            {/* Transaction Name */}
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-user"></i>
                                    </span>
                                    <AutoComplete
                                        placeholder="Name"
                                        field="name"
                                        id="name"
                                        dropdown
                                        value={selectedTransaction}
                                        suggestions={autoFilteredTransaction}
                                        completeMethod={searchTransaction}
                                        onChange={(e) => {
                                            setSelectedTransaction(e.value);
                                            setPayment((prev) => ({
                                                ...prev,
                                                transaction: e.value || null
                                            }));
                                        }}
                                        className={classNames({
                                            'p-invalid': submitted && (!payment.transaction || typeof payment.transaction === 'string' || !payment.transaction.name)
                                        })}
                                    />
                                </div>
                                {submitted && (!payment.transaction || typeof payment.transaction === 'string' || !payment.transaction.name) && <small className="p-invalid">Select a customer.</small>}
                            </div>

                            {/* Coordinator Name */}
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-user"></i>
                                    </span>
                                    <AutoComplete
                                        placeholder="Coordinator Name"
                                        field="coordinator"
                                        id="coordinator"
                                        dropdown
                                        value={payment.transaction && typeof payment.transaction !== 'string' && payment.transaction.coordinator && typeof payment.transaction.coordinator !== 'string' ? payment.transaction.coordinator.firstname : ''}
                                        disabled={loggedUser.role !== 'ADMIN'}
                                    />
                                </div>
                            </div>

                            {/* Transaction Amount */}
                            <InputAmount
                                id="transaxAmount"
                                placeholder="Transaction Amount"
                                value={payment.transaction && typeof payment.transaction !== 'string' ? payment.transaction.amount : 0}
                                disabled={loggedUser.role !== 'ADMIN'}
                                onChange={(e) => {
                                    setPayment((prevPayment: Demo.Payment) => ({
                                        ...prevPayment
                                    }));
                                }}
                                submitted={submitted}
                            />

                            {/* Transaction Installment */}
                            <InputAmount
                                id="transaxInstallment"
                                placeholder="Installment"
                                value={payment.transaction && typeof payment.transaction !== 'string' ? payment.transaction.installment : 0}
                                disabled={loggedUser.role !== 'ADMIN'}
                                onChange={(e) => {
                                    setPayment((prevPayment: Demo.Payment) => ({
                                        ...prevPayment
                                    }));
                                }}
                                submitted={submitted}
                            />
                        </div>
                    </div>

                    {/* Payment Section */}
                    <div className="card">
                        <h5>Payment</h5>
                        <div className="grid p-fluid">
                            {/* Payment Date */}
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <Calendar
                                        showIcon
                                        showButtonBar
                                        disabled={loggedUser.role !== 'ADMIN'}
                                        value={payment.insertDate}
                                        onChange={(e) => {
                                            setPayment((prevPayment: Demo.Payment) => ({
                                                ...prevPayment,
                                                insertDate: Array.isArray(e.value) ? null : new Date(e.value)
                                            }));
                                        }}
                                    />
                                </div>
                            </div>

                            {/* Payment Type */}
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-compass"></i>
                                    </span>
                                    <Dropdown
                                        placeholder="type"
                                        value={payment.type}
                                        options={['PAYMENT', 'PENALTY', 'CAPITAL']}
                                        onChange={(e) => {
                                            setPayment((prevPayment: Demo.Payment) => ({
                                                ...prevPayment,
                                                type: e.value || null
                                            }));
                                        }}
                                    />
                                </div>
                            </div>

                            {/* Payment Amount */}
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-money-bill"></i>
                                    </span>
                                    <InputNumber
                                        id="paymentAmount"
                                        placeholder="Payment Amount"
                                        value={payment.amount}
                                        onChange={(e) => {
                                            setPayment((prevPayment: Demo.Payment) => ({
                                                ...prevPayment,
                                                amount: e.value || null
                                            }));
                                        }}
                                        min={0}
                                        max={payment.transaction && typeof payment.transaction !== 'string' ? payment.transaction.totalAmount : 0}
                                        step={10000}
                                        mode="currency"
                                        currency="XAF"
                                        locale="fr-FR"
                                    />
                                </div>
                                <small className="text-muted">Payment amount</small>
                            </div>

                            {/* Rating */}
                            <div className="col-12 md:col-6">
                                <Rating
                                    value={payment.rating as number}
                                    onChange={(e) => {
                                        setPayment((prevPayment) => ({
                                            ...prevPayment,
                                            rating: e.value || null
                                        }));
                                    }}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Description Section */}
                    <div className="card">
                        <h5>Description</h5>
                        <InputTextarea
                            placeholder="Your Message"
                            value={payment?.description ?? ''}
                            className={classNames({ 'p-invalid': submitted && !payment.description })}
                            autoResize
                            onChange={(e) => {
                                setPayment((prevPayment: Demo.Payment) => ({
                                    ...prevPayment,
                                    description: e.target.value
                                }));
                            }}
                            rows={3}
                            cols={30}
                        />
                    </div>

                    {/* Latest Payments Section */}
                    <div className="card">
                        <DataTable
                            value={latestPayments}
                            header={headerTemplate('Latest Payments', onDialogGlobalFilterChange)}
                            paginator
                            rows={5}
                            responsiveLayout="scroll"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                            rowsPerPageOptions={[5, 10, 25]}
                            filters={dialogFilters}
                            loading={loading}
                            emptyMessage="No payments found for this transaction"
                            sortField={sortField}
                            sortOrder={sortOrder}
                            onSort={(e) => {
                                setSortField(e.sortField);
                                setSortOrder(e.sortOrder as 0 | 1 | -1);
                            }}
                            style={{ width: '100%' }}
                        >
                            <Column field="insertDate" header="Date" sortable body={(rowData) => dateBodyTemplate(rowData.insertDate)} headerClassName="white-space-nowrap" style={{ width: '120px' }} />
                            <Column field="amount" header="Amount" body={(rowData) => currencyBodyTemplate(rowData.amount)} sortable headerClassName="white-space-nowrap" style={{ width: '100px' }} />
                            <Column field="installment" header="Installment" body={(rowData) => currencyBodyTemplate(rowData.installment)} sortable headerClassName="white-space-nowrap" style={{ width: '100px' }} />
                            <Column field="type" header="Type" body={(rowData) => typeBodyTemplate(rowData.type)} sortable headerClassName="white-space-nowrap" style={{ width: '100px' }} />
                            <Column field="rating" header="Rating" body={(rowData) => ratingBodyTemplate(rowData.rating)} sortable style={{ width: '80px' }} />
                            <Column field="description" header="Description" headerClassName="white-space-nowrap" style={{ minWidth: '150px', flexGrow: 1 }} />
                        </DataTable>
                    </div>
                </div>
            </Dialog>
        </div>
    );
};

export default Payment;
