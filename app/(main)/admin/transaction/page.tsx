'use client';
import { Filter<PERSON>atchMode, FilterOperator } from 'primereact/api';
import { AutoComplete, AutoCompleteCompleteEvent } from 'primereact/autocomplete';
import { Column } from 'primereact/column';
import { DataTable, DataTableFilterMeta } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Toast } from 'primereact/toast';
import { Toolbar } from 'primereact/toolbar';
import { classNames } from 'primereact/utils';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { loggedUser } from '../../../../assets/constants';
import { InputActivity, InputAmount, InputPercentage } from '../../../../components/inputs';
import { useDashboardContext } from '../../../../context/use-dashboard-context';
import { useTransactions } from '../../../../hooks/useTransactions';
import type { Demo } from '../../../../types/types';
import {
    coordinatorBodyTemplate,
    coordinatorBodyTemplate_,
    currencyBodyTemplate,
    dateBodyTemplate,
    dialogFooter,
    headerTemplate,
    leftToolbarTemplate,
    percentageBodyTemplate,
    ratingBodyTemplate,
    rightToolbarTemplate,
    statusBodyTemplate
} from '../../utilities/grids/grid';
import { InputNumber } from 'primereact/inputnumber';

// Initial state constants
const initTransaction: Demo.Transaction = {
    uuid: null,
    insertDate: new Date(),
    customer: null,
    coordinator: null,
    amount: 200000,
    coordinatorInterest: 0.15,
    houseInterest: 0.15,
    installment: 130000,
    rating: 5,
    status: 'RUNNING'
};

const Transaction = () => {
    // ===== Table state =====
    const [filters, setFilters] = useState<DataTableFilterMeta>({});
    const [globalFilterValue, setGlobalFilterValue] = useState('');

    // ===== Dialog state =====
    const [dialogFilters, setDialogFilters] = useState<DataTableFilterMeta>({});
    const [dialogGlobalFilterValue, setDialogGlobalFilterValue] = useState('');

    // ===== Customer selection state =====
    const [selectedCustomer, setSelectedCustomer] = useState<Demo.Customer | null>(null);
    const [autoCustomer, setAutoCustomer] = useState<Demo.Customer[]>([]);
    const [selectedCoordinator, setSelectedCoordinator] = useState<Demo.Customer | null>(null);
    const [autoCoordinator, setAutoCoordinator] = useState<Demo.Customer[]>([]);

    const [autoFilteredCustomer, setAutoFilteredCustomer] = useState<Demo.Customer[]>([]);
    const [autoFilteredCoordinator, setAutoFilteredCoordinator] = useState<Demo.Customer[]>([]);

    // ===== Transaction form state =====
    const [submitted, setSubmitted] = useState(false);
    const [transaction, setTransaction] = useState<Demo.Transaction>(initTransaction);
    const [transactionDialog, setTransactionDialog] = useState(false);

    // ===== Context hooks =====
    const { toast, customers, uuidToCustomer, transactions, setTransactions, loading: contextLoading, sortField, setSortField, sortOrder, setSortOrder } = useDashboardContext();

    // ===== Custom hooks =====
    const { loading: transactionLoading, addTransaction, updateTransaction } = useTransactions(toast);

    // Combined loading state
    const loading = contextLoading || transactionLoading;

    const dt = useRef(null);

    // ===== Initialization functions =====
    const initFilters = () => {
        setFilters({
            global: { value: null, matchMode: FilterMatchMode.CONTAINS },
            amount: { value: null, matchMode: FilterMatchMode.EQUALS },
            totalAmount: { value: null, matchMode: FilterMatchMode.EQUALS },
            installment: { value: null, matchMode: FilterMatchMode.EQUALS },
            insertDate: {
                operator: FilterOperator.AND,
                constraints: [{ value: null, matchMode: FilterMatchMode.DATE_IS }]
            },
            rating: { value: null, matchMode: FilterMatchMode.GREATER_THAN }
        });
        setGlobalFilterValue('');
    };

    // Initialize customer and coordinator lists when customers change
    useEffect(() => {
        if (customers?.length > 0) {
            setAutoCustomer(customers.filter((_customer) => _customer.status === 'ACTIVE' && _customer.roles.includes('CUSTOMER')));
            setAutoCoordinator(customers.filter((_coordinator) => _coordinator.status === 'ACTIVE' && _coordinator.roles.includes('COORDINATOR')));
        }
        initFilters();
    }, [customers]);

    // ===== Computed values =====
    const latestTransaction = useMemo(() => {
        if (!selectedCustomer || !selectedCustomer.uuid) return [];
        const custTxs = transactions
            .filter((tx) => {
                if (!tx.customer) return false;
                if (typeof tx.customer === 'string') {
                    return tx.customer === selectedCustomer.uuid;
                }
                return tx.customer.uuid === selectedCustomer.uuid;
            })
            .sort((a, b) => new Date(b.insertDate).getTime() - new Date(a.insertDate).getTime())
            .slice(0, 10);
        return custTxs;
    }, [selectedCustomer, transactions]);

    // ===== Table event handlers =====
    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        let _filters = { ...filters };

        // Ensure global filter exists before setting value
        if (!_filters['global']) {
            _filters['global'] = { value: null, matchMode: FilterMatchMode.CONTAINS };
        }

        (_filters['global'] as any).value = value;
        setFilters(_filters);
        setGlobalFilterValue(value);
    };

    // ===== Dialog event handlers =====
    const openNew = () => {
        const _transaction: Demo.Transaction = {
            ...initTransaction,
            insertDate: new Date(),
            amount: 200000,
            coordinatorInterest: 0.15,
            houseInterest: 0.15,
            installment: 130000,
            rating: 5,
            status: 'RUNNING'
        };

        setTransaction(_transaction);
        setSelectedCustomer(null);
        setSelectedCoordinator(null);
        setSubmitted(false);
        setTransactionDialog(true);
    };

    const exportCSV = () => {
        dt.current.exportCSV();
    };

    const hideDialog = () => {
        setSubmitted(false);
        setTransactionDialog(false);
        setTransaction(initTransaction);
        setSelectedCustomer(null);
        setSelectedCoordinator(null);
    };

    // ===== Form event handlers =====
    const searchCustomer = (event: AutoCompleteCompleteEvent) => {
        setTimeout(() => {
            const query = event.query.trim().toLowerCase();
            if (!query.length) {
                setAutoFilteredCustomer([...autoCustomer]);
            } else {
                setAutoFilteredCustomer(
                    autoCustomer.filter((customer) => {
                        return customer.firstname.toLowerCase().includes(query) || (customer.lastname && customer.lastname.toLowerCase().includes(query));
                    })
                );
            }
        }, 250);
    };

    const searchCoordinator = (event: AutoCompleteCompleteEvent) => {
        setTimeout(() => {
            const query = event.query.trim().toLowerCase();
            if (!query.length) {
                setAutoFilteredCoordinator([...autoCoordinator]);
            } else {
                setAutoFilteredCoordinator(
                    autoCoordinator.filter((coordinator) => {
                        return coordinator.firstname.toLowerCase().includes(query) || (coordinator.lastname && coordinator.lastname.toLowerCase().includes(query));
                    })
                );
            }
        }, 250);
    };

    const saveTransaction = async () => {
        setSubmitted(true);
        let _transaction = { ...transaction, customer: selectedCustomer };
        if (_transaction.customer && _transaction.coordinator) {
            if (_transaction.uuid) {
                await updateTransaction(_transaction, setTransactions);
            } else {
                await addTransaction(_transaction, setTransactions);
            }

            // Reset all form state
            setTransactionDialog(false);
            setTransaction(initTransaction);
            setSelectedCustomer(null);
            setSelectedCoordinator(null);
            setSubmitted(false);
        }
    };

    // Add this useEffect to handle transaction initialization and updates
    useEffect(() => {
        if (selectedCustomer) {
            let _coordinator = selectedCustomer.coordinator as Demo.Customer;
            let _coordinatorInterest = 0.15;

            if (_coordinator) {
                // Use the coordinator's interest rate if available
                _coordinatorInterest = _coordinator.coordinatorInterest || 0.15;
                setSelectedCoordinator(_coordinator);
            }

            // Calculate base values based on customer
            const baseAmount = selectedCustomer.balance || 200000;
            const houseInterest = 0.15;
            const totalAmount = baseAmount * (1 + houseInterest + _coordinatorInterest);
            const rawInstallment = totalAmount / 6;
            const roundedInstallment = Math.ceil(rawInstallment / 1000) * 1000;

            // Update transaction with calculated values
            setTransaction((prev) => ({
                ...prev,
                customer: selectedCustomer,
                coordinator: _coordinator,
                amount: baseAmount,
                houseInterest: houseInterest,
                coordinatorInterest: _coordinatorInterest,
                installment: roundedInstallment,
                status: 'RUNNING',
                rating: 5,
                insertDate: new Date()
            }));
        }
    }, [selectedCustomer, uuidToCustomer]);

    // Add another useEffect to recalculate when amount or interest rates change
    useEffect(() => {
        if (transaction?.amount) {
            const houseInterest = transaction.houseInterest || 0;
            const coordinatorInterest = transaction.coordinatorInterest || 0;
            const totalAmount = transaction.amount * (1 + houseInterest + coordinatorInterest);

            const rawInstallment = totalAmount / 6;
            const roundedInstallment = Math.ceil(rawInstallment / 1000) * 1000;

            if (roundedInstallment !== transaction.installment) {
                setTransaction((prev) => ({
                    ...prev,
                    installment: roundedInstallment
                }));
            }
        }
    }, [transaction?.amount, transaction?.houseInterest, transaction?.coordinatorInterest]);

    // Helper function to calculate the last installment
    const calculateLastInstallment = (totalAmount: number, installmentAmount: number) => {
        if (!totalAmount || !installmentAmount) return 0;

        const remainder = totalAmount % installmentAmount;

        return remainder < 1000 ? installmentAmount : remainder;
    };

    // ===== Render UI =====
    // Show loading spinner while data is being loaded
    if (loading && transactions.length === 0) {
        return (
            <div className="flex justify-content-center align-items-center" style={{ height: '80vh' }}>
                <ProgressSpinner style={{ width: '50px', height: '50px' }} strokeWidth="4" />
            </div>
        );
    }

    return (
        <div className="card">
            <Toolbar className="mb-4" left={leftToolbarTemplate(openNew)} right={rightToolbarTemplate(exportCSV)}></Toolbar>
            <DataTable
                ref={dt}
                value={transactions}
                header={headerTemplate('Transactions', onGlobalFilterChange)}
                paginator
                rows={50}
                responsiveLayout="scroll"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                rowsPerPageOptions={[25, 50, 100]}
                filters={filters}
                loading={loading}
                sortField={sortField}
                sortOrder={sortOrder}
                onSort={(e) => {
                    setSortField(e.sortField);
                    setSortOrder(e.sortOrder as 0 | 1 | -1);
                }}
            >
                <Column field="insertDate" header="Insert Date" sortable body={(rowData) => dateBodyTemplate(rowData.insertDate)} headerClassName="white-space-nowrap" style={{ width: '10%' }} />
                <Column field="name" header="Customer" sortable headerClassName="white-space-nowrap" style={{ width: '25%' }} />
                <Column field="amount" header="Amount" body={(rowData) => currencyBodyTemplate(rowData.amount)} sortable headerClassName="white-space-nowrap" style={{ width: '10%' }} />
                <Column field="coordinatorInterest" header="%-Coordinator" body={(rowData) => percentageBodyTemplate(rowData.coordinatorInterest)} sortable headerClassName="white-space-nowrap" style={{ width: '10%' }} />
                <Column field="houseInterest" header="%-House" body={(rowData) => percentageBodyTemplate(rowData.houseInterest)} sortable headerClassName="white-space-nowrap" />
                <Column
                    field="totalAmount"
                    header="Total Amount"
                    body={(rowData) => {
                        return currencyBodyTemplate(rowData.amount + rowData.amount * rowData.coordinatorInterest + rowData.amount * rowData.houseInterest);
                    }}
                    sortable
                    headerClassName="white-space-nowrap"
                    style={{ width: '10%' }}
                />
                <Column field="installment" header="Installment" body={(rowData) => currencyBodyTemplate(rowData.installment)} sortable headerClassName="white-space-nowrap" style={{ width: '10%' }} />
                <Column field="coordinator.firstname" header="Coordinator" body={(rowData) => coordinatorBodyTemplate(rowData.coordinator)} headerClassName="white-space-nowrap" style={{ width: '15%' }} sortable></Column>
                <Column field="status" header="Status" body={(rowData) => statusBodyTemplate(rowData.status)} sortable headerClassName="white-space-nowrap" style={{ width: '10%' }} />
                <Column field="rating" header="Rating" body={(rowData) => ratingBodyTemplate(rowData.rating)} sortable />
            </DataTable>

            <Dialog visible={transactionDialog} style={{ width: '90vw', maxWidth: '1400px' }} header="Transaction Details" modal className="p-fluid" footer={dialogFooter(hideDialog, saveTransaction)} onHide={hideDialog}>
                <div className="col-12">
                    <div className="card">
                        <h5>Customer</h5>
                        <div className="grid p-fluid">
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-user"></i>
                                    </span>
                                    <AutoComplete
                                        placeholder="Search customer"
                                        field="firstname"
                                        dropdown
                                        value={selectedCustomer}
                                        suggestions={autoFilteredCustomer}
                                        completeMethod={searchCustomer}
                                        onChange={(e) => {
                                            setSelectedCustomer(e.value);
                                            setTransaction((prev) => ({
                                                ...prev,
                                                customer: e.value || null,
                                                coordinator: e.value?.coordinator || null
                                            }));
                                        }}
                                        // itemTemplate={(customer) => (customer ? `${customer.firstname} ${customer.lastname}` : '')}
                                    />
                                </div>
                                {submitted && (!transaction.customer || typeof transaction.customer === 'string' || !transaction.customer.firstname) && <small className="p-invalid">Select a customer.</small>}
                            </div>
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-user"></i>
                                    </span>
                                    <InputText placeholder="Lastname" value={selectedCustomer?.lastname || ''} disabled />
                                </div>
                                {submitted && (!transaction.customer || typeof transaction.customer === 'string' || !transaction.customer.lastname) && <small className="p-invalid">Select a customer.</small>}
                            </div>

                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-money-bill"></i>
                                    </span>
                                    <InputNumber
                                        id="transaxAmount"
                                        placeholder="Amount"
                                        value={selectedCustomer?.balance}
                                        disabled={loggedUser.role !== 'ADMIN'}
                                        onChange={(e) => {
                                            setTransaction((prevTransaction: Demo.Transaction) => ({
                                                ...prevTransaction,
                                                balance: e.value || null
                                            }));
                                        }}
                                        mode="currency"
                                        currency="XAF"
                                        locale="fr-FR"
                                    />
                                </div>
                                <small className="text-muted">Max Amount</small>
                            </div>

                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-map-marker"></i>
                                    </span>
                                    <InputText id="country" placeholder="Country" value={transaction.customer && typeof transaction.customer !== 'string' ? transaction.customer.address?.country.name : ''} disabled />
                                </div>
                            </div>

                            <InputActivity value={transaction.customer && typeof transaction.customer !== 'string' ? transaction.customer.activity : ''} />

                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-compass"></i>
                                    </span>
                                    <InputText id="status" placeholder="Status" value={transaction.customer && typeof transaction.customer !== 'string' ? transaction.customer.status : ''} disabled />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="card">
                        <h5>Coordinator</h5>
                        <div className="grid p-fluid">
                            {/* Coordinator Name */}
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-user"></i>
                                    </span>

                                    <AutoComplete
                                        placeholder="Search coordinator"
                                        field="firstname"
                                        dropdown
                                        value={selectedCoordinator}
                                        suggestions={autoFilteredCoordinator}
                                        completeMethod={searchCoordinator}
                                        onChange={(e) => {
                                            setSelectedCoordinator(e.value);
                                            setTransaction((prev) => ({
                                                ...prev,
                                                coordinator: e.value || null
                                            }));
                                        }}
                                        itemTemplate={coordinatorBodyTemplate_}
                                        className={classNames({
                                            'p-invalid': submitted && (!transaction.coordinator || typeof transaction.coordinator === 'string' || !transaction.coordinator.firstname)
                                        })}
                                    />
                                </div>
                                {submitted && (!transaction.coordinator || typeof transaction.coordinator === 'string' || !transaction.coordinator.firstname) && <small className="p-invalid">Select a coordinator.</small>}
                            </div>
                            <InputPercentage
                                id="coordinatorInterest"
                                placeholder="Percentage"
                                value={transaction.coordinatorInterest}
                                onChange={(e) => {
                                    setTransaction((prevTransaction) => ({
                                        ...prevTransaction,
                                        coordinatorInterest: e.value || null
                                    }));
                                }}
                            />
                        </div>
                    </div>

                    <div className="card">
                        <h5>Transaction</h5>
                        <div className="grid p-fluid">
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-money-bill"></i>
                                    </span>
                                    <InputNumber
                                        id="amount"
                                        placeholder="Amount"
                                        value={transaction?.amount}
                                        onChange={(e) => {
                                            setTransaction((prevTransaction: Demo.Transaction) => ({
                                                ...prevTransaction,
                                                amount: e.value || null
                                            }));
                                        }}
                                        min={0}
                                        max={selectedCustomer?.balance || 1000000}
                                        mode="currency"
                                        currency="XAF"
                                        locale="fr-FR"
                                    />
                                </div>
                                <small className="text-muted">Transaction amount</small>
                            </div>

                            {/* Total Amount Field (Read-only) */}
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-money-bill"></i>
                                    </span>
                                    <InputNumber
                                        id="amountTotal"
                                        placeholder="Total Amount"
                                        value={transaction?.amount ? transaction.amount * (1 + (transaction.houseInterest || 0) + (transaction.coordinatorInterest || 0)) : 0}
                                        disabled={true}
                                        mode="currency"
                                        currency="XAF"
                                        locale="fr-FR"
                                    />
                                </div>
                                <small className="text-muted">Amount with interest</small>
                            </div>

                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-money-bill"></i>
                                    </span>
                                    <InputNumber
                                        id="installment"
                                        placeholder="Installment"
                                        value={transaction?.installment}
                                        onChange={(e) => {
                                            setTransaction((prev) => ({
                                                ...prev,
                                                installment: e.value
                                            }));
                                        }}
                                        min={transaction?.amount ? Math.ceil(transaction.amount / 6 / 1000) * 1000 : 0}
                                        max={transaction?.amount}
                                        step={10000}
                                        mode="currency"
                                        currency="XAF"
                                        locale="fr-FR"
                                    />
                                </div>
                                <small className="text-muted">Monthly payment amount</small>
                            </div>

                            {/* Last Installment Field (Read-only) */}
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-money-bill"></i>
                                    </span>
                                    <InputNumber
                                        id="lastInstallment"
                                        placeholder="Last Installment"
                                        value={
                                            transaction?.amount && transaction?.installment ? calculateLastInstallment(transaction.amount * (1 + (transaction.houseInterest || 0) + (transaction.coordinatorInterest || 0)), transaction.installment) : 0
                                        }
                                        disabled={true}
                                        mode="currency"
                                        currency="XAF"
                                        locale="fr-FR"
                                    />
                                </div>
                                <small className="text-muted">Final payment amount</small>
                            </div>
                        </div>
                    </div>
                    <div className="card">
                        <h5>Description</h5>
                        <InputTextarea placeholder="Your Message" rows={3} cols={30} />
                    </div>
                    <div className="card">
                        <DataTable
                            ref={dt}
                            value={latestTransaction}
                            header={headerTemplate('Latest Transactions', onGlobalFilterChange)}
                            paginator
                            rows={5}
                            responsiveLayout="scroll"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                            rowsPerPageOptions={[5, 10, 25]}
                            filters={dialogFilters}
                            loading={loading}
                            sortField={sortField}
                            sortOrder={sortOrder}
                            onSort={(e) => {
                                setSortField(e.sortField);
                                setSortOrder(e.sortOrder as 0 | 1 | -1);
                            }}
                            style={{ width: '100%' }}
                        >
                            <Column field="insertDate" header="Insert Date" sortable body={(rowData) => dateBodyTemplate(rowData.insertDate)} headerClassName="white-space-nowrap" style={{ width: '120px' }} />
                            <Column field="amount" header="Amount" body={(rowData) => currencyBodyTemplate(rowData.amount)} sortable headerClassName="white-space-nowrap" style={{ width: '100px' }} />
                            <Column field="coordinatorInterest" header="%-Coord" body={(rowData) => percentageBodyTemplate(rowData.coordinatorInterest)} sortable headerClassName="white-space-nowrap" style={{ width: '80px' }} />
                            <Column field="houseInterest" header="%-House" body={(rowData) => percentageBodyTemplate(rowData.houseInterest)} sortable headerClassName="white-space-nowrap" style={{ width: '80px' }} />
                            <Column
                                field="totalAmount"
                                header="Total"
                                body={(rowData) => {
                                    return currencyBodyTemplate(rowData.amount + rowData.amount * rowData.coordinatorInterest + rowData.amount * rowData.houseInterest);
                                }}
                                sortable
                                headerClassName="white-space-nowrap"
                                style={{ width: '100px' }}
                            />
                            <Column field="installment" header="Installment" body={(rowData) => currencyBodyTemplate(rowData.installment)} sortable headerClassName="white-space-nowrap" style={{ width: '100px' }} />
                            <Column field="status" header="Status" body={(rowData) => statusBodyTemplate(rowData.status)} sortable headerClassName="white-space-nowrap" style={{ width: '80px' }} />
                            <Column field="rating" header="Rating" body={(rowData) => ratingBodyTemplate(rowData.rating)} sortable style={{ width: '80px' }} />
                        </DataTable>
                    </div>
                </div>
            </Dialog>
        </div>
    );
};

export default Transaction;
