import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { ProgressBar } from 'primereact/progressbar';
import { Rating } from 'primereact/rating';
import { ChangeEventHandler } from 'react';
import { Demo, PaymentType, TransactionStatus } from '../../../../types/demo';

// Columns Templates
export const nameBodyTemplate = (name: string) => {
    return (
        <>
            <span className="p-column-title">Name</span>
            {name}
        </>
    );
};

export const countryBodyTemplate = (country: Demo.Country) => {
    return (
        <>
            <img alt="flag" src={`/demo/images/flag/flag_placeholder.png`} className={`flag flag-${country.code}`} width={30} />
            <span style={{ marginLeft: '.5em', verticalAlign: 'middle' }}>{country.name}</span>
        </>
    );
};

export const fullnameBodyTemplate = (customer: Demo.Customer) => {
    return (
        <div className="inline-flex align-items-center">
            <span>{customer ? `${customer.firstname} ${customer.lastname}` : '<PERSON>'}</span>
        </div>
    );
};

export const coordinatorBodyTemplate = (coordinator: Demo.Customer) => {
    return (
        <div className="inline-flex align-items-center">
            <img alt={coordinator?.lastname} src={coordinator ? `/demo/images/avatar/${coordinator.image}` : `/demo/images/avatar/profile2.png`} className="w-2rem mr-2" />
            <span>{coordinator ? coordinator.firstname : 'Max Mustermann'}</span>
        </div>
    );
};

export const coordinatorBodyTemplate_ = (coordinator: any) => {
    return (
        <div className="inline-flex align-items-center">
            <img alt={coordinator.firstname} src={`/demo/images/avatar/${coordinator.image}`} className="w-2rem mr-2" />
            <span>{coordinator.firstname} {coordinator.lastname}</span>
        </div>
    );
};

export const dateBodyTemplate = (date: string) => {
    const parsedDate = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(parsedDate.getTime())) {
        return 'Invalid Date';
    }
    return parsedDate?.toLocaleDateString('us-US', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
    });
};

export const currencyBodyTemplate = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'XAF' }).format(amount);
};

export const percentageBodyTemplate = (value: number) => {
    return `${(value * 100).toFixed(0)}%`;
};

export const typeBodyTemplate = (type: PaymentType) => {
    return (
        <>
            <span className="p-column-title">Type</span>
            <span className={`product-badge type-${type.toLowerCase()}`}>{type}</span>
        </>
    );
};

export const ratingBodyTemplate = (rating: number) => {
    return (
        <>
            <span className="p-column-title">Reviews</span>
            <Rating value={rating} readOnly cancel={false} />
        </>
    );
};

export const activityBodyTemplate = (activity: number) => {
    return <ProgressBar value={activity} showValue={false} style={{ height: '.5rem' }} />;
};

export const statusBodyTemplate = (status: TransactionStatus) => {
    return (
        <>
            <span className="p-column-title">Status</span>
            <span className={`product-badge status-${status.toLowerCase()}`}>{status}</span>
        </>
    );
};

export const mobileBodyTemplate = (contact: Demo.Contract) => {
    return (
        <>
            <span className="p-column-title">Mobile Cassh</span>
            <span className={`product-badge status-phone`}>{contact.mobileCash}</span>
        </>
    );
};



export const whatsAppBodyTemplate = (contact: Demo.Contract) => {
    return (
        <>
            <span className="p-column-title">WhatsApp</span>
            <span className="product-badge status-phone">
                {contact.whatsApp}
            </span>
        </>
    );
};


// Headers Templates
export const headerTemplate = (title: string, onChange: ChangeEventHandler<HTMLInputElement>) => {
    return (
        <div className="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
            <h5 className="m-0">{title}</h5>
            <span className="block mt-2 md:mt-0 p-input-icon-left" style={{ maxWidth: '250px' }}>
                <i className="pi pi-search" />
                <InputText type="search" onChange={onChange} placeholder="Search..." />
            </span>
        </div>
    );
};

// Dialogs Footers
export const dialogFooter = (cancel: any, save: any) => (
    <>
        <Button label="Cancel" icon="pi pi-times" text onClick={cancel} />
        <Button label="Save" icon="pi pi-check" text onClick={save} />
    </>
);

export const leftToolbarTemplate = (onClick: React.MouseEventHandler<HTMLButtonElement>) => {
    return (
        <>
            <div className="my-2">
                <Button type="button" icon="pi pi-calendar-plus" label="Add New" className="w-full sm:w-auto flex-order-0 sm:flex-order-1" onClick={onClick} />
            </div>
        </>
    );
};

export const rightToolbarTemplate = (onClick: React.MouseEventHandler<HTMLButtonElement>) => {
    return (
        <>
            <div className="my-2">
                <Button type="button" icon="pi pi-upload" label="Export" className="w-full sm:w-auto flex-order-0 sm:flex-order-1" outlined onClick={onClick} />
            </div>
        </>
    );
};
