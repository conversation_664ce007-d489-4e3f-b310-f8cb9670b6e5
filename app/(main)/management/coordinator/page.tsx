'use client';
import { FilterMatchMode, FilterOperator } from 'primereact/api';
import { AutoCompleteCompleteEvent } from 'primereact/autocomplete';
import { Button } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable, DataTableFilterMeta } from 'primereact/datatable';
import { Toast } from 'primereact/toast';
import { Toolbar } from 'primereact/toolbar';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { loadCoordinators, selectCoordinators } from '../../../services/coordinatorService';
import { addCustomer, loadCustomers, selectCustomers, selectStatus, updateCustomer } from '../../../../services/customerService';
import { ApiStatus } from '../../../../types/demo';
import type { Demo } from '../../../../types/types';
import { activityBodyTemplate, coordinatorBodyTemplate, dateBodyTemplate, headerTemplate, nameBodyTemplate, statusBodyTemplate } from '../../utilities/grids/grid';

let initCustomer: Demo.Customer = {
    id: null,
    insertDate: new Date(),
    username: null,
    firstname: null,
    lastname: null,
    roles: [],
    birthday: null,
    status: 'PENDING',
    balance: 200000,
    verified: false,
    activity: 0,
    rating: 5,
    image: null,
    coordinator: null
};

const Coordinator = () => {
    const [filters, setFilters] = useState<DataTableFilterMeta>({});

    const [autoCoordinator, setAutoCoordinator] = useState<Demo.Customer[]>([]);
    const [selectedCoordinator, setSelectedCoordinator] = useState(null);
    const [autoFilteredCoordinator, setAutoFilteredCoordinator] = useState<Demo.Customer[]>([]);

    const [submitted, setSubmitted] = useState(false);
    const [globalFilterValue, setGlobalFilterValue] = useState('');

    const [customer, setCustomer] = useState(initCustomer);
    const [customerDialog, setCustomerDialog] = useState(false);

    const dispatch = useDispatch();

    const customers: Demo.Customer[] = useSelector(selectCustomers);
    const customerStatus: ApiStatus = useSelector(selectStatus);
    const coordinators: Demo.Customer[] = useSelector(selectCoordinators);

    const dt = useRef(null);
    const toast = useRef(null);

    const clearFilter = () => {
        initFilters();
    };

    const initFilters = () => {
        setFilters({
            global: { value: null, matchMode: FilterMatchMode.CONTAINS },
            amount: { value: null, matchMode: FilterMatchMode.EQUALS },
            installment: { value: null, matchMode: FilterMatchMode.EQUALS },
            customer: { value: null, matchMode: FilterMatchMode.IN },
            insertDate: {
                operator: FilterOperator.AND,
                constraints: [{ value: null, matchMode: FilterMatchMode.DATE_IS }]
            },
            type: { value: null, matchMode: FilterMatchMode.EQUALS }
        });
        setGlobalFilterValue('');
    };

    useEffect(() => {
        dispatch(loadCustomers() as any);
        initFilters();
    }, [dispatch]);

    useEffect(() => {
        if (customerStatus == 'SUCCEEDED' && coordinators.length === 0) {
            dispatch(loadCoordinators() as any);
        }
        setAutoCoordinator(coordinators);
    }, [customerStatus, coordinators]);

    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        let _filters = { ...filters };
        (_filters['global'] as any).value = value;
        setFilters(_filters);
        setGlobalFilterValue(value);
    };

    const openNew = () => {
        setCustomer(initCustomer);
        setSubmitted(false);
        setCustomerDialog(true);
    };

    const exportCSV = () => {
        dt.current.exportCSV();
    };

    const leftToolbarTemplate = () => {
        return (
            <React.Fragment>
                <div className="my-2">
                    <Button type="button" icon="pi pi-plus" label="Add New" className="w-full sm:w-auto flex-order-0 sm:flex-order-1" outlined onClick={openNew} />
                </div>
            </React.Fragment>
        );
    };

    const rightToolbarTemplate = () => {
        return (
            <React.Fragment>
                <Button severity="help" label="Export" icon="pi pi-upload" onClick={exportCSV} />
            </React.Fragment>
        );
    };

    const hideDialog = () => {
        setSubmitted(false);
        setCustomerDialog(false);
    };

    const searchCoordinator = (event: AutoCompleteCompleteEvent) => {
        setTimeout(() => {
            if (!event.query.trim().length) {
                setAutoFilteredCoordinator([...autoCoordinator]);
            } else {
                setAutoFilteredCoordinator(
                    autoCoordinator.filter((coordinator) => {
                        return (coordinator.firstname.toLowerCase().startsWith(event.query.toLowerCase()) || coordinator.lastname.toLowerCase().startsWith(event.query.toLowerCase())) && coordinator.status === 'ACTIVE';
                    })
                );
            }
        }, 250);
    };

    const saveCustomer = () => {
        setSubmitted(true);

        let _customer = { ...customer, coordinatorUuid: selectedCoordinator.uuid, customer: selectedCoordinator.customer, coordinator: selectedCoordinator.coordinator };

        if (_customer.coordinatorUuid) {
            if (_customer.id) {
                dispatch(updateCustomer(_customer));
                toast.current.show({
                    severity: 'success',
                    summary: 'Successful',
                    detail: 'Customer Updated',
                    life: 3000
                });
            } else {
                dispatch(addCustomer(_customer));
                toast.current.show({
                    severity: 'success',
                    summary: 'Successful',
                    detail: 'Customer Created',
                    life: 3000
                });
            }

            // reset customer
            setCustomerDialog(false);
            setCustomer(initCustomer);
            setSelectedCoordinator(null);
        }
    };

    return (
        <div className="card">
            <Toast ref={toast} />
            <Toolbar className="mb-4" left={leftToolbarTemplate} right={rightToolbarTemplate}></Toolbar>
            <DataTable
                ref={dt}
                value={customers}
                header={headerTemplate('Customers', onGlobalFilterChange)}
                paginator
                rows={50}
                responsiveLayout="scroll"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                rowsPerPageOptions={[25, 50, 100]}
                filters={filters}
                loading={customerStatus == 'LOADING'}
            >
                <Column field="insertDate" header="Join Date" sortable body={dateBodyTemplate} headerClassName="white-space-nowrap" style={{ width: '25%' }}></Column>
                <Column field="name" header="Name" sortable body={nameBodyTemplate} headerClassName="white-space-nowrap" style={{ width: '25%' }}></Column>
                <Column field="country" header="Country" sortable body={dateBodyTemplate} headerClassName="white-space-nowrap" style={{ width: '25%' }}></Column>
                <Column field="birthday" header="Birthday" sortable body={dateBodyTemplate} headerClassName="white-space-nowrap" style={{ width: '25%' }} />
                <Column field="coordinator" header="Created By" body={coordinatorBodyTemplate} headerClassName="white-space-nowrap" style={{ width: '25%' }} sortable></Column>
                <Column field="status" header="Status" body={statusBodyTemplate} sortable headerClassName="white-space-nowrap" style={{ width: '25%' }} />
                <Column field="activity" header="Activity" body={activityBodyTemplate} headerClassName="white-space-nowrap" style={{ width: '25%' }} sortable></Column>
            </DataTable>

            {/* <Dialog visible={customerDialog} style={{ width: '600px' }} header="Customer Details" modal className="p-fluid" footer={dialogFooter(hideDialog, saveCustomer)} onHide={hideDialog}>
                <div className="col-12">
                    <div className="card">
                        <h5>Coordinator</h5>
                        <div className="grid p-fluid">
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-user"></i>
                                    </span>
                                    <AutoComplete
                                        placeholder="Customer Name"
                                        field="customer"
                                        id="customer"
                                        dropdown
                                        value={selectedCoordinator?.customer}
                                        onChange={(e) => {
                                            setSelectedCoordinator((prevCoordinator: Demo.Coordinator) => ({
                                                ...prevCoordinator,
                                                customer: e.value?.customer || e.value || null,
                                                uuid: e.value?.uuid || null,
                                                coordinator: e.value?.coordinator || null,
                                                amount: e.value?.amount || null,
                                                installment: e.value?.installment || null
                                            }));
                                        }}
                                        suggestions={autoFilteredCoordinator}
                                        completeMethod={searchCoordinator}
                                        className={classNames({
                                            'p-invalid': submitted && !selectedCoordinator?.customer
                                        })}
                                    />
                                </div>
                                {submitted && !selectedCoordinator?.customer && <small className="p-invalid">Select a customer.</small>}
                            </div>
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-user"></i>
                                    </span>
                                    <AutoComplete
                                        placeholder="Coordinator Name"
                                        field="coordinator"
                                        id="coordinator"
                                        dropdown
                                        value={selectedCoordinator?.coordinator}
                                        disabled={loggedUser.role !== 'ADMIN'}
                                        onChange={(e) => {
                                            setSelectedCoordinator((prevCoordinator: Demo.Coordinator) => ({
                                                ...prevCoordinator,
                                                coordinator: e.value || null
                                            }));
                                        }}
                                        className={classNames({
                                            'p-invalid': submitted && !selectedCoordinator?.coordinator
                                        })}
                                    />
                                </div>
                                {submitted && !selectedCoordinator?.coordinator && <small className="p-invalid">Select a coordinator.</small>}
                            </div>
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <InputNumber
                                        placeholder="Amount"
                                        id="amount"
                                        value={selectedCoordinator?.amount}
                                        disabled={loggedUser.role !== 'ADMIN'}
                                        onChange={(e) => {
                                            setSelectedCoordinator((prevCoordinator: Demo.Coordinator) => ({
                                                ...prevCoordinator,
                                                amount: e.value || null
                                            }));
                                        }}
                                        className={classNames({
                                            'p-invalid': submitted && !selectedCoordinator?.amount
                                        })}
                                        mode="currency"
                                        currency="XAF"
                                        locale="fr-FR"
                                        showButtons
                                    />
                                </div>
                            </div>
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <InputNumber
                                        placeholder="Installment"
                                        id="installment"
                                        value={selectedCoordinator?.installment}
                                        disabled={loggedUser.role !== 'ADMIN'}
                                        onChange={(e) => {
                                            setSelectedCoordinator((prevCoordinator: Demo.Coordinator) => ({
                                                ...prevCoordinator,
                                                installment: e.value || null
                                            }));
                                        }}
                                        className={classNames({
                                            'p-invalid': submitted && !selectedCoordinator?.amount
                                        })}
                                        mode="currency"
                                        currency="XAF"
                                        locale="fr-FR"
                                        max={selectedCoordinator?.amount}
                                        showButtons
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="card">
                        <h5>Customer</h5>
                        <div className="grid p-fluid">
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <Calendar
                                        showIcon
                                        showButtonBar
                                        disabled={loggedUser.role !== 'ADMIN'}
                                        value={customer.insertDate}
                                        onChange={(e) => {
                                            setCustomer((prevCustomer) => ({
                                                ...prevCustomer,
                                                insertDate: Array.isArray(e.value) ? null : new Date(e.value)
                                            }));
                                        }}
                                    />
                                </div>
                            </div>
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <span className="p-inputgroup-addon">
                                        <i className="pi pi-compass"></i>
                                    </span>
                                    <Dropdown
                                        placeholder="type"
                                        value={customer.type}
                                        options={['PAYMENT', 'PENALTY', 'CAPITAL']}
                                        onChange={(e) => {
                                            setCustomer((prevCustomer) => ({
                                                ...prevCustomer,
                                                type: e.value || null
                                            }));
                                        }}
                                    />
                                </div>
                            </div>
                            <div className="col-12 md:col-6">
                                <div className="p-inputgroup">
                                    <InputNumber
                                        placeholder="Amount"
                                        value={customer.amount}
                                        onValueChange={(e) => {
                                            setCustomer((prevCustomer) => ({
                                                ...prevCustomer,
                                                amount: e.value || null
                                            }));
                                        }}
                                        mode="currency"
                                        currency="XAF"
                                        locale="fr-FR"
                                        min={100000}
                                        max={500000}
                                        step={100000}
                                        showButtons
                                    />
                                </div>
                            </div>
                            <div className="col-12 md:col-6">
                                <Rating
                                    value={customer.rating as number}
                                    onChange={(e) => {
                                        setCustomer((prevCustomer) => ({
                                            ...prevCustomer,
                                            rating: e.value || null
                                        }));
                                    }}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="card">
                        <h5>Description</h5>
                        <InputTextarea
                            placeholder="Your Message"
                            value={customer.description}
                            onChange={(e) => {
                                setCustomer((prevCustomer) => ({
                                    ...prevCustomer,
                                    description: e.target.value
                                }));
                            }}
                            rows={3}
                            cols={30}
                        />
                    </div>
                </div>
            </Dialog> */}
        </div>
    );
};

export default Coordinator;
