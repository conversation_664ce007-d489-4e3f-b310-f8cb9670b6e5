import { ColorPicker } from 'primereact/colorpicker';
// FullCalendar Types
import { EventApi, EventInput } from '@fullcalendar/core';

// Chart.js Types
import { ChartData, ChartOptions } from 'chart.js';
import { C, N, S } from '@fullcalendar/core/internal-common';

// Customer Status
export type CustomerStatus = 'ACTIVE' | 'INACTIVE' | 'BLOCKED';
type Role = 'ADMIN' | 'COORDINATOR' | 'CUSTOMER';

// Transaction Status
export type TransactionStatus = 'REQUESTED' | 'REJECTED' | 'RUNNING' | 'COMPLETED' | 'DELAYED' | 'STOPPED' | 'ARCHIVE';

// Payment Types
export type PaymentType = 'PAYMENT' | 'PENALTY' | 'CAPITAL' | 'OTHERS';

// Custom Types
type ApiStatus = 'IDLE' | 'LOADING' | 'SUCCEEDED' | 'FAILED';
type InventoryStatus = 'INSTOCK' | 'LOWSTOCK' | 'OUTOFSTOCK';
type SmallFolder = Omit<BaseFolder, 'icon'> & { icon: 'pi pi-folder' | 'pi pi-images' | 'pi pi-folder-open' };
type LargeFolder = Omit<BaseFolder, 'icon'> & { icon: 'pi pi-folder' | 'pi pi-image' | 'pi pi-folder-open' };
type Icon = 'pi pi-image' | 'pi pi-file-excel' | 'pi pi-file-pdf' | 'pi pi-ellipsis-v' | 'pi pi-folder' | 'pi pi-images' | 'pi pi-folder-open';
type Color = 'bg-yellow-500' | 'bg-pink-500' | 'bg-green-500' | 'bg-indigo-500';
type MailKeys = 'important' | 'starred' | 'trash' | 'spam' | 'archived' | 'sent';

// Exported Types
export type LayoutType = 'list' | 'grid';
export type SortOrderType = 1 | 0 | -1;

// Interfaces
export interface CustomEvent {
    name?: string;
    status?: 'Ordered' | 'Processing' | 'Shipped' | 'Delivered';
    date?: string;
    color?: string;
    icon?: string;
    image?: string;
}

interface ShowOptions {
    severity?: string;
    content?: string;
    summary?: string;
    detail?: string;
    life?: number;
}

export interface ChartDataState {
    barData?: ChartData;
    pieData?: ChartData;
    lineData?: ChartData;
    polarData?: ChartData;
    radarData?: ChartData;
}
export interface ChartOptionsState {
    barOptions?: ChartOptions;
    pieOptions?: ChartOptions;
    lineOptions?: ChartOptions;
    polarOptions?: ChartOptions;
    radarOptions?: ChartOptions;
}

export interface AppMailProps {
    mails: Demo.Mail[];
}

export interface AppMailSidebarItem {
    label: string;
    icon: string;
    to?: string;
    badge?: number;
    badgeValue?: number;
}

export interface AppMailReplyProps {
    content: Demo.Mail | null;
    hide: () => void;
}

// Services Interfaces
interface ICustomerService {
    loadCustomers(): Promise<Demo.Customer[]>;
    addCustomer(transaction: Demo.Customer): Promise<Demo.Customer>;
    updateCustomer(transaction: Demo.Customer): Promise<Demo.Customer>;
    removeCustomer(uuid: string): Promise<string>;
}

interface ITransactionService {
    loadTransactions(customers: Demo.Customer[]): Promise<Demo.Transaction[]>;
    addTransaction(transaction: Demo.Transaction): Promise<Demo.Transaction>;
    updateTransaction(transaction: Demo.Transaction): Promise<Demo.Transaction>;
    removeTransaction(uuid: string): Promise<string>;
}

interface IPaymentService {
    loadPayments(transactions: Demo.Transaction[]): Promise<Demo.Payment[]>;
    addPayment(transaction: Demo.Payment): Promise<Demo.Payment>;
    updatePayment(transaction: Demo.Payment): Promise<Demo.Payment>;
    removePayment(uuid: string): Promise<string>;
}

// Demo Namespace
declare namespace Demo {
    // Interfaces
    interface Base {
        name: string;
        icon: Icon;
        objectURL?: string;
    }

    interface IFile extends Base {
        date: string;
        fileSize: string;
    }

    interface Metric {
        title: string;
        icon: string;
        color_light: string;
        color_dark: string;
        textContent: MetricContent[];
        color?: string;
        fieldColor?: string;
        files?: string;
        fileSize?: string;
    }
    interface BaseFolder extends Base {
        size: string;
    }
    interface Task {
        id?: number;
        name?: string;
        description?: string;
        completed?: boolean;
        status?: string;
        comments?: string;
        attachments?: string;
        members?: Member[];
        startDate?: string;
        endDate?: string;
    }

    interface Member {
        name: string;
        image: string;
    }

    interface DialogConfig {
        visible: boolean;
        header: string;
        newTask: boolean;
    }

    interface Mail {
        id: number;
        from: string;
        to: string;
        email: string;
        image: string;
        title: string;
        message: string;
        date: string;
        important: boolean;
        starred: boolean;
        trash: boolean;
        spam: boolean;
        archived: boolean;
        sent: boolean;
    }

    interface User {
        id: number;
        name: string;
        image: string;
        status: string;
        messages: Message[];
        lastSeen: string;
    }

    interface Message {
        text: string;
        ownerId: number;
        createdAt: number;
    }
    interface MetricContent {
        amount: number;
        status: string;
        format?: string;
    }

    //ProductService
    type Product = {
        id?: string;
        code?: string;
        name: string;
        description: string;
        image?: string;
        price?: number | string;
        category?: string;
        quantity?: number;
        inventoryStatus?: InventoryStatus;
        rating?: number;
        orders?: ProductOrder[];
        [key: string]: string | string[] | number | boolean | undefined | ProductOrder[] | InventoryStatus | File[];
    };

    type ProductOrder = {
        id?: string;
        productCode?: string;
        date?: string;
        amount?: number;
        quantity?: number;
        customer?: string;
        status?: Status;
    };

    //CustomerService
    type Customer = {
        uuid: string;
        image?: string;
        insertDate?: string | Date;
        username: string;
        password?: string;
        firstname: string;
        lastname: string;
        birthday: string | Date;
        contact: Contract;
        address: Address;
        roles: Role[];
        status: CustomerStatus;
        balance?: number;
        activity?: number;
        rating?: number;
        coordinator: string | Customer;
        coordinatorInterest?: number;
    };

    //TransactionService
    type Transaction = {
        uuid: string;
        insertDate: string | Date;
        name?: string;
        customer: string | Customer;
        coordinator: string | Customer;
        amount: number;
        coordinatorInterest?: number;
        houseInterest?: number;
        totalAmount?: number;
        installment?: number;
        status: TransactionStatus;
        rating?: number;
        payments?: [] | Payment[];
        description?: string;
    };

    //PaymentService
    type Payment = {
        uuid: string;
        insertDate: string | Date;
        name?: string;
        amount: number;
        type: PaymentType;
        rating: number;
        installment: number;
        transaction: string | Transaction;
        description?: string;
    };

    // EventService
    interface Event extends EventInput {
        location?: string;
        description?: string;
        tag?: {
            name: string;
            color: string;
        };
    }

    // PhotoService
    type Photo = {
        title: string;
        itemImageSrc?: string | undefined;
        thumbnailImageSrc?: string | undefined;
        alt?: string | undefined;
    };

    type Country = {
        code: string;
        name: string;
        areaCode?: string;
        currency?: string;
    };

    type Contract = {
        email: string;
        phone: string;
        whatsApp?: string;
        mobileCash?: string;
    };

    type Address = {
        street: String;
        city: String;
        zip: String;
        country: Country;
    };

    // IconService
    type Icon = {
        icon?: {
            paths?: string[];
            attrs?: [{}];
            isMulticolor?: boolean;
            isMulticolor2?: boolean;
            grid?: number;
            tags?: string[];
        };
        attrs?: [{}];
        properties?: {
            order?: number;
            id: number;
            name: string;
            prevSize?: number;
            code?: number;
        };
        setIdx?: number;
        setId?: number;
        iconIdx?: number;
    };
}
