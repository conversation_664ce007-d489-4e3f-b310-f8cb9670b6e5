'use client';

import { useSession } from 'next-auth/react';
import { Toast } from 'primereact/toast';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useCustomers } from '../hooks/useCustomers';
import { usePayments } from '../hooks/usePayments';
import { useTransactions } from '../hooks/useTransactions';
import { Demo } from '../types/demo';
import { DashboardContext } from './use-dashboard-context';

const DashboardProvider = ({ children }: { children: React.ReactNode }) => {
    const toast = useRef<any>(null);
    const { data: session } = useSession();

    // State management
    const [dataLoaded, setDataLoaded] = useState(false);
    const [loading, setLoading] = useState(false);
    const [loggedUser, setLoggedUser] = useState<Demo.Customer | null>(null);
    const [customers, setCustomers] = useState<Demo.Customer[]>([]);
    const [transactions, setTransactions] = useState<Demo.Transaction[]>([]);
    const [payments, setPayments] = useState<Demo.Payment[]>([]);
    const [uuidToCustomer, setUuidToCustomer] = useState<Record<string, Demo.Customer>>({});
    const [uuidToTransaction, setUuidToTransaction] = useState<Record<string, Demo.Transaction>>({});

    // Sorting state
    const [sortField, setSortField] = useState<string>('insertDate');
    const [sortOrder, setSortOrder] = useState<0 | 1 | -1>(-1);

    // Add metrics state with pre-populated properties
    const [metrics, setMetrics] = useState<Demo.Metric[]>([
        {
            title: 'Transactions',
            icon: 'pi pi-shopping-cart',
            color_light: '#64B5F6',
            color_dark: '#1976D2',
            textContent: [
                { amount: 0, status: 'RUNNING' as const },
                { amount: 0, status: 'COMPLETED' as const }
            ]
        },
        {
            title: 'Revenue',
            icon: 'pi pi-dollar',
            color_light: '#7986CB',
            color_dark: '#303F9F',
            textContent: [
                { amount: 0, status: 'RUNNING' as const },
                { amount: 0, status: 'COMPLETED' as const }
            ]
        },
        {
            title: 'Payments',
            icon: 'pi pi-credit-card',
            color_light: '#4DB6AC',
            color_dark: '#00796B',
            textContent: [
                { amount: 0, status: 'PAYMENT' as const },
                { amount: 0, status: 'PENALTY' as const }
            ]
        },
        {
            title: 'Customers',
            icon: 'pi pi-users',
            color_light: '#4DD0E1',
            color_dark: '#0097A7',
            textContent: [
                { amount: 0, status: 'ACTIVE' as const },
                { amount: 0, status: 'INACTIVE' as const }
            ]
        }
    ]);

    // Custom hooks
    const { loadCustomers } = useCustomers(toast);
    const { loadTransactions } = useTransactions(toast);
    const { loadPayments } = usePayments(toast);

    // Centralized data loading
    useEffect(() => {
        const loadAllData = async () => {
            if (session && !dataLoaded && !loading) {
                setLoading(true);
                try {
                    const _customers = await loadCustomers();
                    if (_customers) {
                        const _uuidToCustomer = _customers.reduce((acc: Record<string, Demo.Customer>, customer: Demo.Customer) => {
                            if (customer.uuid) {
                                acc[customer.uuid] = customer;
                            }
                            return acc;
                        }, {});

                        const _mappedCustomers = _customers.map((_customer: Demo.Customer) => ({
                            ..._customer,
                            coordinator: _uuidToCustomer[_customer.coordinator as string]
                        })) as Demo.Customer[];

                        setCustomers(_mappedCustomers);
                        setUuidToCustomer(_uuidToCustomer);

                        const _transactions = await loadTransactions(_customers, _uuidToCustomer);
                        if (_transactions) {
                            const _uuidToTransaction = _transactions.reduce((acc: Record<string, Demo.Transaction>, transaction: Demo.Transaction) => {
                                if (transaction.uuid) {
                                    acc[transaction.uuid] = transaction;
                                }
                                return acc;
                            }, {});

                            setTransactions(_transactions);
                            setUuidToTransaction(_uuidToTransaction);

                            const _payments = await loadPayments(_transactions, _uuidToTransaction);
                            if (_payments) {
                                setPayments(_payments);
                            }
                        }
                    }

                    setDataLoaded(true);
                } catch (error) {
                    console.error('Failed to load data:', error);
                    toast.current?.show({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Failed to load application data',
                        life: 3000
                    });
                } finally {
                    setLoading(false);
                }
            }
        };

        loadAllData();
    }, [session, dataLoaded, loading, loadCustomers, loadTransactions, loadPayments]);

    // Update metrics when data changes
    useEffect(() => {
        if (dataLoaded) {
            const currentYear = new Date().getFullYear();

            const _runningTransactions = transactions.filter((t) => {
                const txDate = new Date(t.insertDate);
                return txDate.getFullYear() === currentYear && t.status === 'RUNNING';
            });

            const _completedTransactions = transactions.filter((t) => {
                const txDate = new Date(t.insertDate);
                return txDate.getFullYear() === currentYear && t.status === 'COMPLETED';
            });

            const _runningRevenue =
                _runningTransactions.reduce((sum, t) => {
                    const houseInterest = t.houseInterest || 0;
                    const houseRevenue = t.amount * houseInterest;
                    return sum + houseRevenue;
                }, 0) / 1000;

            const _completedRevenue =
                _completedTransactions.reduce((sum, t) => {
                    const houseInterest = t.houseInterest || 0;
                    const houseRevenue = t.amount * houseInterest;
                    return sum + houseRevenue;
                }, 0) / 1000;

            const _paymentsForYear = payments.filter((p) => {
                const paymentDate = new Date(p.insertDate);
                return paymentDate.getFullYear() === currentYear;
            });

            const _payment = _paymentsForYear.filter((p) => p.type === 'PAYMENT').reduce((sum, p) => sum + (p.amount || 0), 0);
            const _penalty = _paymentsForYear.filter((p) => p.type === 'PENALTY').reduce((sum, p) => sum + (p.amount || 0), 0);

            const _activeCustomers = customers.filter((c) => c.status === 'ACTIVE').length;
            const _inactiveCustomers = customers.filter((c) => c.status === 'INACTIVE').length;

            setMetrics([
                {
                    title: 'Transactions',
                    icon: 'pi pi-shopping-cart',
                    color_light: '#64B5F6',
                    color_dark: '#1976D2',
                    textContent: [
                        { amount: _runningTransactions.length, status: 'RUNNING' },
                        { amount: _completedTransactions.length, status: 'COMPLETED' }
                    ]
                },
                {
                    title: 'Revenue',
                    icon: 'pi pi-dollar',
                    color_light: '#7986CB',
                    color_dark: '#303F9F',
                    textContent: [
                        { amount: _runningRevenue, status: 'RUNNING', format: 'currency' },
                        { amount: _completedRevenue, status: 'COMPLETED', format: 'currency' }
                    ]
                },
                {
                    title: 'Payments',
                    icon: 'pi pi-credit-card',
                    color_light: '#4DB6AC',
                    color_dark: '#00796B',
                    textContent: [
                        { amount: _payment, status: 'PAYMENT', format: 'currency' },
                        { amount: _penalty, status: 'PENALTY', format: 'currency' }
                    ]
                },
                {
                    title: 'Customers',
                    icon: 'pi pi-users',
                    color_light: '#4DD0E1',
                    color_dark: '#0097A7',
                    textContent: [
                        { amount: _activeCustomers, status: 'ACTIVE' },
                        { amount: _inactiveCustomers, status: 'INACTIVE' }
                    ]
                }
            ]);
        }
    }, [dataLoaded, transactions, payments, customers]);

    // Context value
    const value = useMemo(
        () => ({
            loading,
            customers,
            setCustomers,
            transactions,
            setTransactions,
            payments,
            setPayments,
            loggedUser,
            setLoggedUser,
            uuidToCustomer,
            setUuidToCustomer,
            uuidToTransaction,
            setUuidToTransaction,
            sortField,
            setSortField,
            sortOrder,
            setSortOrder,
            toast,
            // Add a method to refresh data if needed
            refreshData: () => setDataLoaded(false),
            metrics
        }),
        [customers, transactions, payments, loggedUser, loading, toast, uuidToCustomer, uuidToTransaction, sortField, sortOrder, metrics]
    );

    return (
        <>
            <Toast ref={toast} />
            <DashboardContext.Provider value={value}>{children}</DashboardContext.Provider>
        </>
    );
};

export default DashboardProvider;
