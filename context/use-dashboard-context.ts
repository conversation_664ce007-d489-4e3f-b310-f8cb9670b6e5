'use client';

import { createContext, Dispatch, SetStateAction, useContext } from 'react';
import { Demo } from '../types/demo';

interface DashboardContextType {
  toast: React.MutableRefObject<any> | null;
  loading: boolean;

  loggedUser: Demo.Customer | null;
  setLoggedUser: Dispatch<SetStateAction<Demo.Customer>>;

  customers: Demo.Customer[];
  setCustomers: Dispatch<SetStateAction<Demo.Customer[]>>;

  uuidToCustomer: Record<string, Demo.Customer>;
  setUuidToCustomer: Dispatch<SetStateAction<Record<string, Demo.Customer>>>;

  transactions: Demo.Transaction[];
  setTransactions: Dispatch<SetStateAction<Demo.Transaction[]>>;

  uuidToTransaction: Record<string, Demo.Transaction>;
  setUuidToTransaction: Dispatch<SetStateAction<Record<string, Demo.Transaction>>>;

  payments: Demo.Payment[];
  setPayments: Dispatch<SetStateAction<Demo.Payment[]>>;
  
  // Sorting state
  sortField: string;
  setSortField: Dispatch<SetStateAction<string>>;
  sortOrder: 0 | 1 | -1;
  setSortOrder: Dispatch<SetStateAction<0 | 1 | -1>>;
  
  refreshData: () => void;
  metrics: Demo.Metric[];
}

const defaultContextValue: DashboardContextType = {
  toast: null,
  loading: false,
  
  loggedUser: null,
  setLoggedUser: () => {},

  customers: [],
  setCustomers: () => {},

  uuidToCustomer: {},
  setUuidToCustomer: () => {},

  transactions: [],
  setTransactions: () => {},

  uuidToTransaction: {},
  setUuidToTransaction: () => {},

  payments: [],
  setPayments: () => {},
  
  // Sorting state
  sortField: 'insertDate',
  setSortField: () => {},
  sortOrder: -1,
  setSortOrder: () => {},
  
  refreshData: () => {},
  metrics: []
};

export const DashboardContext = createContext<DashboardContextType>(defaultContextValue);

export const useDashboardContext = () => useContext(DashboardContext);
