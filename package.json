{"name": "apart-ways-app", "version": "10.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/**/*.{js,ts,tsx}\""}, "dependencies": {"@fullcalendar/core": "6.1.4", "@fullcalendar/daygrid": "6.1.4", "@fullcalendar/interaction": "6.1.4", "@fullcalendar/react": "6.1.4", "@fullcalendar/timegrid": "6.1.4", "@reduxjs/toolkit": "^2.0.1", "@types/uuid": "^10.0.0", "chart.js": "4.3.2", "next": "^15.2.3", "next-auth": "^5.0.0-beta.25", "primeflex": "3.3.0", "primeicons": "^6.0.1", "primereact": "9.6.2", "quill": "^1.3.7", "react": "18.2.0", "react-dom": "18.2.0", "react-redux": "^9.0.4", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "18.15.11", "@types/react": "^18.2.45", "@types/react-dom": "18.0.11", "@types/react-transition-group": "^4.4.1", "eslint": "8.35.0", "eslint-config-next": "13.4.9", "prettier": "2.8.4", "sass": "^1.58.3", "typescript": "5.0.4"}}