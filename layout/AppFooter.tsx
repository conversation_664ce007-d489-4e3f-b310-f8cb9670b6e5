'use client';

import { useContext } from 'react';
import { LayoutContext } from '../context/LayoutProvider';

const AppFooter = () => {
    const { layoutConfig } = useContext(LayoutContext);

    return (
        <div className="layout-footer">
            <div className="footer-logo-container">
                <img src={`/layout/images/logo-${layoutConfig.colorScheme === 'light' ? 'dark' : 'white'}.svg`} alt="diamond-layout" />
                <span className="footer-app-name">ASPIRE</span>
            </div>
            {/* APART GmbH: Accelerating Progress with Assets, Resources and Technology - Accélérer le Progrès avec des Actifs, des Ressources et de la Technologie */}
            <span className="footer-copyright">&#169; Smart Solutions - 2025</span>
        </div>
    );
};

export default AppFooter;
