import { InputNumber } from 'primereact/inputnumber';
import { classNames } from 'primereact/utils';

export const InputActivity = ({ value }) => {
    return (
        <div className="col-12 md:col-6">
            <div className="p-inputgroup">
                <span className="p-inputgroup-addon">
                    <i className="pi pi-history"></i>
                </span>
                <InputNumber id="activity" placeholder="Activity" value={value} disabled />
            </div>
        </div>
    );
};

export const InputAmount = ({ id, placeholder, value, disabled = false, onChange, submitted, min = null, max = null, step = 100000 }) => {
    return (
        <div className="col-12 md:col-6">
            <div className="p-inputgroup">
                <InputNumber
                    id={id}
                    placeholder={placeholder}
                    value={value}
                    onChange={onChange}
                    disabled={disabled}
                    className={classNames({
                        'p-invalid': submitted && !value
                    })}
                    mode="currency"
                    currency="XAF"
                    locale="fr-FR"
                    min={min}
                    max={max}
                    step={step}
                    showButtons
                />
            </div>
        </div>
    );
};

export const InputPercentage = ({ id, placeholder, value, disabled = false, onChange }) => {
    return (
        <div className="col-12 md:col-6">
            <div className="p-inputgroup">
                <span className="p-inputgroup-addon">
                    <i className="pi pi-globe"></i>
                </span>
                <InputNumber id={id} placeholder={placeholder} value={value} disabled={disabled} onValueChange={onChange} min={0.05} max={0.15} step={0.05} mode="decimal" showButtons />
            </div>
        </div>
    );
};
