import { signIn } from 'next-auth/react';
import { Button } from 'primereact/button';
import { useState } from 'react';

export const OAuthSignInButton = ({ label, provider, icon }) => {
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const handleLogin = async (e) => {
        e.preventDefault();
        try {
            setIsLoading(true);
            await signIn(provider, { callbackUrl: 'http://localhost:3000/overview/dashboard' });
        } catch (error) {
            console.error(error);
            setIsLoading(false);
        } finally {
            setIsLoading(false);
        }
    };

    return <Button disabled={isLoading} label={label} className="w-20rem" icon={isLoading ? 'pi pi-spin pi-spinner' : icon} onClick={handleLogin}></Button>;
};
