/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: true,

    env: {
        API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL
    },

    webpack: (config, { isServer }) => {
        return config;
    },

    async redirects() {
        return [
            {
                source: '/',
                destination: '/overview/dashboard',
                permanent: true
            },
            {
                source: '/apps/mail',
                destination: '/apps/mail/inbox',
                permanent: true
            }
        ];
    }
};

module.exports = nextConfig;
