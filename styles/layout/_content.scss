.layout-content-wrapper {
    display: flex;
    flex-direction: column; 
    justify-content: space-between; 
    min-height: 100vh;
    transition: margin-left .4s cubic-bezier(.05,.74,.2,.99);
}

.layout-content {
    padding: 2rem;
    padding-top: 6rem;
    flex: 1 1 auto;
    width: 100%; // Ensure full width
    max-width: 100%; // Don't let it exceed container
    margin: 0 auto; // Center content
    
    // Responsive width adjustments
    @media screen and (min-width: 1200px) {
        width: 95%; // Slightly narrower on large screens
        max-width: 1800px; // But cap the maximum width
    }
    
    @media screen and (min-width: 1800px) {
        width: 90%; // Even narrower on very large screens
        max-width: 2000px; // With a higher maximum
    }
}

// Add a utility class for full-width content when needed
.layout-content-full {
    width: 100% !important;
    max-width: 100% !important;
}
