* {
    box-sizing: border-box;
}
  
html {
    height: 100%;
    font-size: $scale;
}

body {
    font-family: var(--font-family);
    font-weight: 400;
    color: var(--text-color);
    padding: 0;
    margin: 0;
    min-height: 100%;
    background: var(--surface-ground);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    text-decoration: none;
}

.layout-mask {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 998;
    width: 100%;
    height: 100%;
    animation-duration: .4s;
    animation-timing-function: cubic-bezier(.05,.74,.2,.99);
    animation-fill-mode: forwards;
}

.grid {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    
    // Ensure columns expand properly
    > .col, > [class*="col-"] {
        box-sizing: border-box;
    }
    
    .grid {
        margin-left: -0.5rem;
        margin-right: -0.5rem;
    }
}

.card {
    width: 100%;
    box-sizing: border-box;
}

.p-datatable {
    width: 100%;
}
