Fix transaction handling and improve customer mapping

- Fix type error in useTransactions.ts by properly handling customer UUID extraction
- Resolve non-serializable Date object in Redux action by converting to ISO string
- Add centralized uuidToCustomer map in DashboardContext for efficient customer lookup
- Update DashboardProvider to maintain customer UUID mapping
- Improve code reusability and performance by sharing customer mapping across components