import { MenuModal, MenuTheme } from '../types/layout';

export const loggedUser = {
    name: '<PERSON>',
    email: 'b.ngue<PERSON>@gmail.com',
    avatar: 'assets/layout/images/avatar_1.png',
    role: 'CUSTOMER' // ADMIN, COORDINATOR, CUSTOMER
};

export const model: MenuModal[] = [
    {
        label: 'Overview',
        icon: 'pi pi-home',
        items: [
            {
                label: 'Dashboard',
                icon: 'pi pi-fw pi-home',
                to: '/overview/dashboard'
            },
            {
                label: 'Banking',
                icon: 'pi pi-fw pi-image',
                to: '/overview/banking'
            }
        ]
    },
    { separator: true },
    {
        label: 'Administration',
        icon: 'pi pi-fw pi-user',
        items: [
            {
                label: 'Payment',
                icon: 'pi pi-fw pi-dollar',
                to: '/admin/payment',
                visible: true
            },
            {
                label: 'Transaction',
                icon: 'pi pi-fw pi-cart-plus',
                to: '/admin/transaction'
            },
            {
                label: 'Customer',
                icon: 'pi pi-fw pi-user',
                to: '/admin/customer'
            }
        ]
    },
    { separator: true },
    {
        label: 'Management',
        icon: 'pi pi-fw pi-download',
        items: [
            {
                label: 'Transaction',
                icon: 'pi pi-fw pi-cart-plus',
                to: '/management/transaction'
            },
            {
                label: 'Customer',
                icon: 'pi pi-fw pi-user',
                to: '/management/customer'
            },
            {
                label: 'Coordinator',
                icon: 'pi pi-fw pi-folder',
                to: '/management/coordinator'
            }
        ]
    },
    { separator: true },
    {
        label: 'Apps',
        icon: 'pi pi-th-large',
        items: [
            {
                label: 'Calendar',
                icon: 'pi pi-fw pi-calendar',
                to: '/apps/calendar'
            },
            {
                label: 'Chat',
                icon: 'pi pi-fw pi-comments',
                to: '/apps/chat'
            },
            {
                label: 'Mail',
                icon: 'pi pi-fw pi-envelope',
                items: [
                    {
                        label: 'Inbox',
                        icon: 'pi pi-fw pi-inbox',
                        to: '/apps/mail/inbox'
                    },
                    {
                        label: 'Compose',
                        icon: 'pi pi-fw pi-pencil',
                        to: '/apps/mail/compose'
                    },
                    {
                        label: 'Detail',
                        icon: 'pi pi-fw pi-comment',
                        to: '/apps/mail/detail/1000'
                    }
                ]
            },
            {
                label: 'Task List',
                icon: 'pi pi-fw pi-check-square',
                to: '/apps/tasklist'
            }
        ]
    },
    { separator: true },
    {
        label: 'Start',
        icon: 'pi pi-fw pi-download',
        items: [
            {
                label: 'Buy Now',
                icon: 'pi pi-fw pi-shopping-cart',
                url: 'https://www.primefaces.org/store'
            },
            {
                label: 'Documentation',
                icon: 'pi pi-fw pi-info-circle',
                to: '/documentation'
            }
        ],
        visible: false
    }
];

export const menuThemes: MenuTheme[] = [
    {
        name: 'white',
        color: '#ffffff',
        logoColor: 'dark',
        componentTheme: 'dark',
        visible: true
    },
    {
        name: 'darkgray',
        color: '#343a40',
        logoColor: 'white',
        componentTheme: 'white',
        visible: false
    },
    {
        name: 'blue',
        color: '#1976d2',
        logoColor: 'white',
        componentTheme: 'blue',
        visible: true
    },
    {
        name: 'bluegray',
        color: '#455a64',
        logoColor: 'white',
        componentTheme: 'lightgreen',
        visible: true
    },
    {
        name: 'brown',
        color: '#5d4037',
        logoColor: 'white',
        componentTheme: 'cyan',
        visible: true
    },
    {
        name: 'cyan',
        color: '#0097a7',
        logoColor: 'white',
        componentTheme: 'cyan',
        visible: false
    },
    {
        name: 'green',
        color: '#388e3C',
        logoColor: 'white',
        componentTheme: 'green',
        visible: false
    },
    {
        name: 'indigo',
        color: '#303f9f',
        logoColor: 'white',
        componentTheme: 'indigo',
        visible: true
    },
    {
        name: 'deeppurple',
        color: '#512da8',
        logoColor: 'white',
        componentTheme: 'deeppurple',
        visible: false
    },
    {
        name: 'orange',
        color: '#F57c00',
        logoColor: 'dark',
        componentTheme: 'orange',
        visible: false
    },
    {
        name: 'pink',
        color: '#c2185b',
        logoColor: 'white',
        componentTheme: 'pink',
        visible: false
    },
    {
        name: 'purple',
        color: '#7b1fa2',
        logoColor: 'white',
        componentTheme: 'purple',
        visible: false
    },
    {
        name: 'teal',
        color: '#00796b',
        logoColor: 'white',
        componentTheme: 'teal',
        visible: true
    }
];

export const componentThemes = [
    { name: 'blue', color: '#42A5F5' },
    { name: 'green', color: '#66BB6A' },
    { name: 'lightgreen', color: '#9CCC65' },
    // { name: 'purple', color: '#AB47BC' },
    { name: 'deeppurple', color: '#7E57C2' },
    { name: 'indigo', color: '#5C6BC0' },
    // { name: 'orange', color: '#FFA726' },
    { name: 'cyan', color: '#26C6DA' }
    // { name: 'pink', color: '#EC407A' },
    // { name: 'teal', color: '#26A69A' }
];

export const scales = [12, 13, 14, 15, 16];

export const countries = [
    { name: 'Gabon', code: 'ga' },
    { name: 'Germany', code: 'de' },
    { name: 'France', code: 'fr' }
];
