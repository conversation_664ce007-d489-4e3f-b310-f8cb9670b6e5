import { v4 as uuidv4 } from 'uuid';

/**
 * Generates a UUID v4
 * @returns UUID string
 */
export const generateUuid = () => {
    return uuidv4();
};

// Only import Node.js modules on the server side
let fs;
let path;
let os;

// Check if we're in a Node.js environment
if (typeof window === 'undefined') {
    // Server-side only imports
    fs = require('fs');
    path = require('path');
    os = require('os');
}

/**
 * Resolves a file path, handling home directory references
 * Server-side only function
 * @param filePath Path to resolve
 * @returns Resolved absolute path
 */
export const resolvePath = (filePath: string) => {
    if (typeof window !== 'undefined') {
        console.error('resolvePath can only be used on the server side');
        return filePath || '';
    }
    
    if (filePath?.startsWith('~/')) {
        return path.join(os.homedir(), filePath.slice(2));
    }
    return filePath || '';
};

/**
 * Ensures a file exists, creating it with an empty array if it doesn't
 * Server-side only function
 * @param filePath Path to the file
 */
export const ensureFileExists = (filePath: string) => {
    if (typeof window !== 'undefined') {
        console.error('ensureFileExists can only be used on the server side');
        return;
    }
    
    if (!fs.existsSync(filePath)) {
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        fs.writeFileSync(filePath, JSON.stringify([], null, 2), 'utf8');
    }
};

/**
 * Reads data from a JSON file
 * Server-side only function
 * @param filePath Path to the JSON file
 * @returns Parsed JSON data
 */
export const readJsonFile = <T>(filePath: string): T[] => {
    if (typeof window !== 'undefined') {
        console.error('readJsonFile can only be used on the server side');
        return [];
    }
    
    ensureFileExists(filePath);
    const data = fs.readFileSync(filePath, 'utf8');
    try {
        return JSON.parse(data) as T[];
    } catch (error) {
        console.error(`Error parsing JSON file ${filePath}:`, error);
        return [];
    }
};

/**
 * Writes data to a JSON file
 * Server-side only function
 * @param filePath Path to the JSON file
 * @param data Data to write
 */
export const writeJsonFile = <T>(filePath: string, data: T[]): void => {
    if (typeof window !== 'undefined') {
        console.error('writeJsonFile can only be used on the server side');
        return;
    }
    
    ensureFileExists(filePath);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
};

/**
 * Gets the full path to a data file
 * Server-side only function
 * @param basePath Base API path
 * @param filename Filename (e.g., 'customers.json')
 * @returns Full resolved path
 */
export const getDataFilePath = (basePath: string, filename: string): string => {
    if (typeof window !== 'undefined') {
        console.error('getDataFilePath can only be used on the server side');
        return filename;
    }
    
    const resolvedPath = resolvePath(basePath);
    return path.join(resolvedPath, filename);
};
